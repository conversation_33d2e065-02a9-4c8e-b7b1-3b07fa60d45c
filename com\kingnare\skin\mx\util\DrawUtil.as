package com.kingnare.skin.mx.util
{
   import flash.display.GradientType;
   import flash.display.Graphics;
   import flash.geom.Matrix;
   
   public class DrawUtil
   {
      
      public function DrawUtil()
      {
         super();
      }
      
      public static function drawArrow(param1:Graphics, param2:Number, param3:Array, param4:Array, param5:Number, param6:Number, param7:Boolean = true) : void
      {
         var _loc8_:Number = 0;
         var _loc9_:int = 1;
         var _loc10_:uint = 0;
         param2 += 2;
         if(!param7)
         {
            _loc10_ = Math.floor(param2 / 2);
            _loc9_ = -1;
         }
         while(param2 - 2 > -2)
         {
            drawSingleRect(param1,param3,param4,param5 + _loc8_,param6 + _loc10_ + _loc9_ * _loc8_,param2,1);
            _loc8_++;
            param2 -= 2;
         }
      }
      
      public static function drawDoubleRect(param1:Graphics, param2:Array, param3:Array, param4:Number = 0, param5:Number = 0, param6:Number = 0, param7:Number = 0, param8:Number = 0, param9:Number = 0, param10:Number = 0, param11:Number = 0, param12:Boolean = false) : void
      {
         if(param12)
         {
            param1.clear();
         }
         var _loc13_:Matrix = new Matrix();
         _loc13_.createGradientBox(param6,param7,Math.PI / 2,0,0);
         param1.beginGradientFill(GradientType.LINEAR,param2,param3,null,_loc13_);
         param1.drawRect(param4,param5,param6,param7);
         param1.drawRect(param8,param9,param10,param11);
         param1.endFill();
      }
      
      public static function drawSingleRect(param1:Graphics, param2:Array, param3:Array, param4:Number = 0, param5:Number = 0, param6:Number = 0, param7:Number = 0, param8:Boolean = false) : void
      {
         if(param8)
         {
            param1.clear();
         }
         var _loc9_:Matrix = new Matrix();
         _loc9_.createGradientBox(param6,param7,Math.PI / 2,0,0);
         param1.beginGradientFill(GradientType.LINEAR,param2,param3,null,_loc9_);
         param1.drawRect(param4,param5,param6,param7);
         param1.endFill();
      }
   }
}

