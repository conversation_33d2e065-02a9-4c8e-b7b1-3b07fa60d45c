package com.kingnare.skin.spark.TabBar
{
   import flash.accessibility.*;
   import flash.debugger.*;
   import flash.display.*;
   import flash.errors.*;
   import flash.events.*;
   import flash.external.*;
   import flash.geom.*;
   import flash.media.*;
   import flash.net.*;
   import flash.printing.*;
   import flash.profiler.*;
   import flash.system.*;
   import flash.text.*;
   import flash.ui.*;
   import flash.utils.*;
   import flash.xml.*;
   import mx.binding.*;
   import mx.core.IFlexModuleFactory;
   import mx.core.IStateClient2;
   import mx.core.mx_internal;
   import mx.events.PropertyChangeEvent;
   import mx.filters.*;
   import mx.graphics.GradientEntry;
   import mx.graphics.LinearGradient;
   import mx.graphics.LinearGradientStroke;
   import mx.graphics.SolidColor;
   import mx.graphics.SolidColorStroke;
   import mx.states.SetProperty;
   import mx.states.State;
   import mx.styles.*;
   import spark.components.Group;
   import spark.components.Label;
   import spark.primitives.Rect;
   import spark.skins.SparkButtonSkin;
   
   use namespace mx_internal;
   
   public class TabBarButtonSkin extends SparkButtonSkin implements IBindingClient, IStateClient2
   {
      
      private static var _watcherSetupUtil:IWatcherSetupUtil2;
      
      private static const exclusions:Array = ["labelDisplay"];
      
      private var _746881822_TabBarButtonSkin_GradientEntry1:GradientEntry;
      
      private var _746881821_TabBarButtonSkin_GradientEntry2:GradientEntry;
      
      private var _746881820_TabBarButtonSkin_GradientEntry3:GradientEntry;
      
      private var _746881819_TabBarButtonSkin_GradientEntry4:GradientEntry;
      
      private var _746881817_TabBarButtonSkin_GradientEntry6:GradientEntry;
      
      private var _334252641barMask:Group;
      
      private var _1383304148border:Rect;
      
      private var _3143043fill:Rect;
      
      private var _728661292fillbase:Rect;
      
      private var _1507289076highlightStroke:Rect;
      
      private var _1306546406lowlightStroke:Rect;
      
      private var __moduleFactoryInitialized:Boolean = false;
      
      private var cornerRadius:Number = 4;
      
      mx_internal var _bindings:Array;
      
      mx_internal var _watchers:Array;
      
      mx_internal var _bindingsByDestination:Object;
      
      mx_internal var _bindingsBeginWithWord:Object;
      
      public function TabBarButtonSkin()
      {
         var watchers:Array;
         var i:uint;
         var bindings:Array = null;
         var target:Object = null;
         var watcherSetupUtilClass:Object = null;
         this.mx_internal::_bindings = [];
         this.mx_internal::_watchers = [];
         this.mx_internal::_bindingsByDestination = {};
         this.mx_internal::_bindingsBeginWithWord = {};
         super();
         mx_internal::_document = this;
         bindings = this._TabBarButtonSkin_bindingsSetup();
         watchers = [];
         target = this;
         if(_watcherSetupUtil == null)
         {
            watcherSetupUtilClass = getDefinitionByName("_com_kingnare_skin_spark_TabBar_TabBarButtonSkinWatcherSetupUtil");
            watcherSetupUtilClass["init"](null);
         }
         _watcherSetupUtil.setup(this,function(param1:String):*
         {
            return target[param1];
         },function(param1:String):*
         {
            return TabBarButtonSkin[param1];
         },bindings,watchers);
         mx_internal::_bindings = mx_internal::_bindings.concat(bindings);
         mx_internal::_watchers = mx_internal::_watchers.concat(watchers);
         this.minWidth = 21;
         this.minHeight = 21;
         this.mxmlContent = [this._TabBarButtonSkin_Rect1_i(),this._TabBarButtonSkin_Rect2_i(),this._TabBarButtonSkin_Rect3_i(),this._TabBarButtonSkin_Rect4_i(),this._TabBarButtonSkin_Group1_i(),this._TabBarButtonSkin_Rect6_i(),this._TabBarButtonSkin_Rect7_c(),this._TabBarButtonSkin_Rect8_c(),this._TabBarButtonSkin_Label1_i()];
         this.currentState = "up";
         states = [new State({
            "name":"up",
            "overrides":[]
         }),new State({
            "name":"over",
            "stateGroups":["overStates"],
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry1",
               "name":"alpha",
               "value":0.15
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry2",
               "name":"alpha",
               "value":0.05
            })]
         }),new State({
            "name":"down",
            "stateGroups":["downStates"],
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry1",
               "name":"alpha",
               "value":0.05
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry1",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry2",
               "name":"alpha",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry2",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry2",
               "name":"ratio",
               "value":0.8
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry3",
               "name":"alpha",
               "value":0.2
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry3",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry4",
               "name":"alpha",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry4",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry4",
               "name":"ratio",
               "value":0.8
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry6",
               "name":"alpha",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry6",
               "name":"ratio",
               "value":0.8
            }),new SetProperty().initializeFromObject({
               "target":"barMask",
               "name":"bottom",
               "value":1
            })]
         }),new State({
            "name":"disabled",
            "stateGroups":["disabledStates"],
            "overrides":[new SetProperty().initializeFromObject({
               "name":"alpha",
               "value":0.5
            })]
         }),new State({
            "name":"upAndSelected",
            "stateGroups":["selectedUpStates","selectedStates"],
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry1",
               "name":"alpha",
               "value":0.05
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry1",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry2",
               "name":"alpha",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry2",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry2",
               "name":"ratio",
               "value":0.8
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry3",
               "name":"alpha",
               "value":0.2
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry3",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry4",
               "name":"alpha",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry4",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry4",
               "name":"ratio",
               "value":0.8
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry6",
               "name":"alpha",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry6",
               "name":"ratio",
               "value":0.8
            }),new SetProperty().initializeFromObject({
               "target":"barMask",
               "name":"bottom",
               "value":1
            })]
         }),new State({
            "name":"overAndSelected",
            "stateGroups":["overStates","selectedStates"],
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry1",
               "name":"alpha",
               "value":0.05
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry1",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry2",
               "name":"alpha",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry2",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry2",
               "name":"ratio",
               "value":0.8
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry3",
               "name":"alpha",
               "value":0.2
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry3",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry4",
               "name":"alpha",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry4",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry4",
               "name":"ratio",
               "value":0.8
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry6",
               "name":"alpha",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry6",
               "name":"ratio",
               "value":0.8
            }),new SetProperty().initializeFromObject({
               "target":"barMask",
               "name":"bottom",
               "value":1
            })]
         }),new State({
            "name":"downAndSelected",
            "stateGroups":["downStates","selectedStates"],
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry1",
               "name":"alpha",
               "value":0.05
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry1",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry2",
               "name":"alpha",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry2",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry2",
               "name":"ratio",
               "value":0.8
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry3",
               "name":"alpha",
               "value":0.2
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry3",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry4",
               "name":"alpha",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry4",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry4",
               "name":"ratio",
               "value":0.8
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry6",
               "name":"alpha",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry6",
               "name":"ratio",
               "value":0.8
            }),new SetProperty().initializeFromObject({
               "target":"barMask",
               "name":"bottom",
               "value":1
            })]
         }),new State({
            "name":"disabledAndSelected",
            "stateGroups":["disabledStates","selectedUpStates","selectedStates"],
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry1",
               "name":"alpha",
               "value":0.05
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry1",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry2",
               "name":"alpha",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry2",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry2",
               "name":"ratio",
               "value":0.8
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry3",
               "name":"alpha",
               "value":0.2
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry3",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry4",
               "name":"alpha",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry4",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry4",
               "name":"ratio",
               "value":0.8
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry6",
               "name":"alpha",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TabBarButtonSkin_GradientEntry6",
               "name":"ratio",
               "value":0.8
            }),new SetProperty().initializeFromObject({
               "target":"barMask",
               "name":"bottom",
               "value":1
            })]
         })];
         i = 0;
         while(i < bindings.length)
         {
            Binding(bindings[i]).execute();
            i++;
         }
      }
      
      public static function set watcherSetupUtil(param1:IWatcherSetupUtil2) : void
      {
         TabBarButtonSkin._watcherSetupUtil = param1;
      }
      
      override public function set moduleFactory(param1:IFlexModuleFactory) : void
      {
         super.moduleFactory = param1;
         if(this.__moduleFactoryInitialized)
         {
            return;
         }
         this.__moduleFactoryInitialized = true;
      }
      
      override public function initialize() : void
      {
         super.initialize();
      }
      
      override public function get colorizeExclusions() : Array
      {
         return exclusions;
      }
      
      override protected function initializationComplete() : void
      {
         useChromeColor = true;
         super.initializationComplete();
      }
      
      private function createPathData(param1:Boolean) : String
      {
         var _loc2_:Number = 0;
         var _loc3_:Number = width;
         var _loc4_:Number = 0.5;
         var _loc5_:Number = height;
         var _loc6_:Number = this.cornerRadius * 0.292893218813453;
         var _loc7_:Number = this.cornerRadius * 0.585786437626905;
         var _loc8_:String = "";
         _loc8_ = _loc8_ + ("M " + _loc2_ + " " + _loc5_);
         _loc8_ = _loc8_ + (" L " + _loc2_ + " " + (_loc4_ + this.cornerRadius));
         _loc8_ = _loc8_ + (" Q " + _loc2_ + " " + (_loc4_ + _loc7_) + " " + (_loc2_ + _loc6_) + " " + (_loc4_ + _loc6_));
         _loc8_ = _loc8_ + (" Q " + (_loc2_ + _loc7_) + " " + _loc4_ + " " + (_loc2_ + this.cornerRadius) + " " + _loc4_);
         if(param1)
         {
            _loc8_ += " L " + (_loc3_ - this.cornerRadius) + " " + _loc4_;
         }
         else
         {
            _loc8_ += " M " + (_loc3_ - this.cornerRadius) + " " + _loc4_;
         }
         _loc8_ += " Q " + (_loc3_ - _loc7_) + " " + _loc4_ + " " + (_loc3_ - _loc6_) + " " + (_loc4_ + _loc6_);
         _loc8_ = _loc8_ + (" Q " + _loc3_ + " " + (_loc4_ + _loc7_) + " " + _loc3_ + " " + (_loc4_ + this.cornerRadius));
         return _loc8_ + (" L " + _loc3_ + " " + _loc5_);
      }
      
      private function updateCornerRadius() : void
      {
         var _loc1_:Number = getStyle("cornerRadius");
         if(this.cornerRadius != _loc1_)
         {
            this.cornerRadius = _loc1_;
            this.fill.topLeftRadiusX = this.cornerRadius;
            this.fill.topRightRadiusX = this.cornerRadius;
            this.highlightStroke.topLeftRadiusX = this.cornerRadius;
            this.highlightStroke.topRightRadiusX = this.cornerRadius;
         }
      }
      
      override protected function updateDisplayList(param1:Number, param2:Number) : void
      {
         this.updateCornerRadius();
         super.updateDisplayList(param1,unscaledHeight);
      }
      
      private function _TabBarButtonSkin_Rect1_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 0;
         _loc1_.right = 0;
         _loc1_.top = 0;
         _loc1_.bottom = 0;
         _loc1_.topLeftRadiusX = 4;
         _loc1_.topRightRadiusX = 4;
         _loc1_.width = 72;
         _loc1_.height = 24;
         _loc1_.fill = this._TabBarButtonSkin_SolidColor1_c();
         _loc1_.initialized(this,"fillbase");
         this.fillbase = _loc1_;
         BindingManager.executeBindings(this,"fillbase",this.fillbase);
         return _loc1_;
      }
      
      private function _TabBarButtonSkin_SolidColor1_c() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 3355443;
         return _loc1_;
      }
      
      private function _TabBarButtonSkin_Rect2_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 1;
         _loc1_.right = 1;
         _loc1_.top = 1;
         _loc1_.bottom = 1;
         _loc1_.topLeftRadiusX = 4;
         _loc1_.topRightRadiusX = 4;
         _loc1_.width = 70;
         _loc1_.height = 22;
         _loc1_.fill = this._TabBarButtonSkin_LinearGradient1_c();
         _loc1_.initialized(this,"fill");
         this.fill = _loc1_;
         BindingManager.executeBindings(this,"fill",this.fill);
         return _loc1_;
      }
      
      private function _TabBarButtonSkin_LinearGradient1_c() : LinearGradient
      {
         var _loc1_:LinearGradient = new LinearGradient();
         _loc1_.rotation = 90;
         _loc1_.entries = [this._TabBarButtonSkin_GradientEntry1_i(),this._TabBarButtonSkin_GradientEntry2_i()];
         return _loc1_;
      }
      
      private function _TabBarButtonSkin_GradientEntry1_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 16777215;
         _loc1_.alpha = 0.08;
         this._TabBarButtonSkin_GradientEntry1 = _loc1_;
         BindingManager.executeBindings(this,"_TabBarButtonSkin_GradientEntry1",this._TabBarButtonSkin_GradientEntry1);
         return _loc1_;
      }
      
      private function _TabBarButtonSkin_GradientEntry2_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 16777215;
         _loc1_.alpha = 0.03;
         this._TabBarButtonSkin_GradientEntry2 = _loc1_;
         BindingManager.executeBindings(this,"_TabBarButtonSkin_GradientEntry2",this._TabBarButtonSkin_GradientEntry2);
         return _loc1_;
      }
      
      private function _TabBarButtonSkin_Rect3_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 1;
         _loc1_.right = 1;
         _loc1_.top = 1;
         _loc1_.bottom = 1;
         _loc1_.topLeftRadiusX = 4;
         _loc1_.topRightRadiusX = 4;
         _loc1_.stroke = this._TabBarButtonSkin_LinearGradientStroke1_c();
         _loc1_.initialized(this,"highlightStroke");
         this.highlightStroke = _loc1_;
         BindingManager.executeBindings(this,"highlightStroke",this.highlightStroke);
         return _loc1_;
      }
      
      private function _TabBarButtonSkin_LinearGradientStroke1_c() : LinearGradientStroke
      {
         var _loc1_:LinearGradientStroke = new LinearGradientStroke();
         _loc1_.rotation = 90;
         _loc1_.weight = 1;
         _loc1_.entries = [this._TabBarButtonSkin_GradientEntry3_i(),this._TabBarButtonSkin_GradientEntry4_i()];
         return _loc1_;
      }
      
      private function _TabBarButtonSkin_GradientEntry3_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 16777215;
         _loc1_.alpha = 0.1;
         this._TabBarButtonSkin_GradientEntry3 = _loc1_;
         BindingManager.executeBindings(this,"_TabBarButtonSkin_GradientEntry3",this._TabBarButtonSkin_GradientEntry3);
         return _loc1_;
      }
      
      private function _TabBarButtonSkin_GradientEntry4_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 16777215;
         _loc1_.alpha = 0.03;
         this._TabBarButtonSkin_GradientEntry4 = _loc1_;
         BindingManager.executeBindings(this,"_TabBarButtonSkin_GradientEntry4",this._TabBarButtonSkin_GradientEntry4);
         return _loc1_;
      }
      
      private function _TabBarButtonSkin_Rect4_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 2;
         _loc1_.right = 2;
         _loc1_.top = 2;
         _loc1_.bottom = 2;
         _loc1_.topLeftRadiusX = 4;
         _loc1_.topRightRadiusX = 4;
         _loc1_.stroke = this._TabBarButtonSkin_LinearGradientStroke2_c();
         _loc1_.initialized(this,"lowlightStroke");
         this.lowlightStroke = _loc1_;
         BindingManager.executeBindings(this,"lowlightStroke",this.lowlightStroke);
         return _loc1_;
      }
      
      private function _TabBarButtonSkin_LinearGradientStroke2_c() : LinearGradientStroke
      {
         var _loc1_:LinearGradientStroke = new LinearGradientStroke();
         _loc1_.rotation = 90;
         _loc1_.weight = 1;
         _loc1_.entries = [this._TabBarButtonSkin_GradientEntry5_c(),this._TabBarButtonSkin_GradientEntry6_i()];
         return _loc1_;
      }
      
      private function _TabBarButtonSkin_GradientEntry5_c() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 0;
         _loc1_.alpha = 0.08;
         return _loc1_;
      }
      
      private function _TabBarButtonSkin_GradientEntry6_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 0;
         _loc1_.alpha = 0.03;
         this._TabBarButtonSkin_GradientEntry6 = _loc1_;
         BindingManager.executeBindings(this,"_TabBarButtonSkin_GradientEntry6",this._TabBarButtonSkin_GradientEntry6);
         return _loc1_;
      }
      
      private function _TabBarButtonSkin_Group1_i() : Group
      {
         var _loc1_:Group = new Group();
         _loc1_.left = 0;
         _loc1_.right = 0;
         _loc1_.top = 0;
         _loc1_.bottom = 0;
         _loc1_.mxmlContent = [this._TabBarButtonSkin_Rect5_c()];
         _loc1_.id = "barMask";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         this.barMask = _loc1_;
         BindingManager.executeBindings(this,"barMask",this.barMask);
         return _loc1_;
      }
      
      private function _TabBarButtonSkin_Rect5_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.percentWidth = 100;
         _loc1_.percentHeight = 100;
         _loc1_.fill = this._TabBarButtonSkin_SolidColor2_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _TabBarButtonSkin_SolidColor2_c() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 16711680;
         return _loc1_;
      }
      
      private function _TabBarButtonSkin_Rect6_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 0;
         _loc1_.right = 0;
         _loc1_.top = 0;
         _loc1_.bottom = 0;
         _loc1_.topLeftRadiusX = 4;
         _loc1_.topRightRadiusX = 4;
         _loc1_.stroke = this._TabBarButtonSkin_SolidColorStroke1_c();
         _loc1_.initialized(this,"border");
         this.border = _loc1_;
         BindingManager.executeBindings(this,"border",this.border);
         return _loc1_;
      }
      
      private function _TabBarButtonSkin_SolidColorStroke1_c() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.color = 0;
         return _loc1_;
      }
      
      private function _TabBarButtonSkin_Rect7_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 0;
         _loc1_.bottom = 0;
         _loc1_.width = 1;
         _loc1_.height = 1;
         _loc1_.fill = this._TabBarButtonSkin_SolidColor3_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _TabBarButtonSkin_SolidColor3_c() : SolidColor
      {
         return new SolidColor();
      }
      
      private function _TabBarButtonSkin_Rect8_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.right = 0;
         _loc1_.bottom = 0;
         _loc1_.width = 1;
         _loc1_.height = 1;
         _loc1_.fill = this._TabBarButtonSkin_SolidColor4_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _TabBarButtonSkin_SolidColor4_c() : SolidColor
      {
         return new SolidColor();
      }
      
      private function _TabBarButtonSkin_Label1_i() : Label
      {
         var _loc1_:Label = new Label();
         _loc1_.maxDisplayedLines = 1;
         _loc1_.horizontalCenter = 0;
         _loc1_.verticalCenter = 1;
         _loc1_.left = 10;
         _loc1_.right = 10;
         _loc1_.top = 2;
         _loc1_.bottom = 2;
         _loc1_.setStyle("textAlign","center");
         _loc1_.setStyle("verticalAlign","middle");
         _loc1_.id = "labelDisplay";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         labelDisplay = _loc1_;
         BindingManager.executeBindings(this,"labelDisplay",labelDisplay);
         return _loc1_;
      }
      
      private function _TabBarButtonSkin_bindingsSetup() : Array
      {
         var _loc1_:Array = [];
         _loc1_[0] = new Binding(this,null,null,"border.mask","barMask");
         return _loc1_;
      }
      
      [Bindable(event="propertyChange")]
      public function get _TabBarButtonSkin_GradientEntry1() : GradientEntry
      {
         return this._746881822_TabBarButtonSkin_GradientEntry1;
      }
      
      public function set _TabBarButtonSkin_GradientEntry1(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._746881822_TabBarButtonSkin_GradientEntry1;
         if(_loc2_ !== param1)
         {
            this._746881822_TabBarButtonSkin_GradientEntry1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_TabBarButtonSkin_GradientEntry1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _TabBarButtonSkin_GradientEntry2() : GradientEntry
      {
         return this._746881821_TabBarButtonSkin_GradientEntry2;
      }
      
      public function set _TabBarButtonSkin_GradientEntry2(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._746881821_TabBarButtonSkin_GradientEntry2;
         if(_loc2_ !== param1)
         {
            this._746881821_TabBarButtonSkin_GradientEntry2 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_TabBarButtonSkin_GradientEntry2",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _TabBarButtonSkin_GradientEntry3() : GradientEntry
      {
         return this._746881820_TabBarButtonSkin_GradientEntry3;
      }
      
      public function set _TabBarButtonSkin_GradientEntry3(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._746881820_TabBarButtonSkin_GradientEntry3;
         if(_loc2_ !== param1)
         {
            this._746881820_TabBarButtonSkin_GradientEntry3 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_TabBarButtonSkin_GradientEntry3",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _TabBarButtonSkin_GradientEntry4() : GradientEntry
      {
         return this._746881819_TabBarButtonSkin_GradientEntry4;
      }
      
      public function set _TabBarButtonSkin_GradientEntry4(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._746881819_TabBarButtonSkin_GradientEntry4;
         if(_loc2_ !== param1)
         {
            this._746881819_TabBarButtonSkin_GradientEntry4 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_TabBarButtonSkin_GradientEntry4",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _TabBarButtonSkin_GradientEntry6() : GradientEntry
      {
         return this._746881817_TabBarButtonSkin_GradientEntry6;
      }
      
      public function set _TabBarButtonSkin_GradientEntry6(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._746881817_TabBarButtonSkin_GradientEntry6;
         if(_loc2_ !== param1)
         {
            this._746881817_TabBarButtonSkin_GradientEntry6 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_TabBarButtonSkin_GradientEntry6",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get barMask() : Group
      {
         return this._334252641barMask;
      }
      
      public function set barMask(param1:Group) : void
      {
         var _loc2_:Object = this._334252641barMask;
         if(_loc2_ !== param1)
         {
            this._334252641barMask = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"barMask",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get border() : Rect
      {
         return this._1383304148border;
      }
      
      public function set border(param1:Rect) : void
      {
         var _loc2_:Object = this._1383304148border;
         if(_loc2_ !== param1)
         {
            this._1383304148border = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"border",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get fill() : Rect
      {
         return this._3143043fill;
      }
      
      public function set fill(param1:Rect) : void
      {
         var _loc2_:Object = this._3143043fill;
         if(_loc2_ !== param1)
         {
            this._3143043fill = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"fill",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get fillbase() : Rect
      {
         return this._728661292fillbase;
      }
      
      public function set fillbase(param1:Rect) : void
      {
         var _loc2_:Object = this._728661292fillbase;
         if(_loc2_ !== param1)
         {
            this._728661292fillbase = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"fillbase",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get highlightStroke() : Rect
      {
         return this._1507289076highlightStroke;
      }
      
      public function set highlightStroke(param1:Rect) : void
      {
         var _loc2_:Object = this._1507289076highlightStroke;
         if(_loc2_ !== param1)
         {
            this._1507289076highlightStroke = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"highlightStroke",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get lowlightStroke() : Rect
      {
         return this._1306546406lowlightStroke;
      }
      
      public function set lowlightStroke(param1:Rect) : void
      {
         var _loc2_:Object = this._1306546406lowlightStroke;
         if(_loc2_ !== param1)
         {
            this._1306546406lowlightStroke = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"lowlightStroke",_loc2_,param1));
            }
         }
      }
   }
}

