package com.kingnare.skin.spark.VideoPlayer
{
   import com.kingnare.skin.spark.VideoPlayer.fullScreen.FullScreenButtonSkin;
   import com.kingnare.skin.spark.VideoPlayer.fullScreen.PlayPauseButtonSkin;
   import com.kingnare.skin.spark.VideoPlayer.fullScreen.ScrubBarSkin;
   import com.kingnare.skin.spark.VideoPlayer.fullScreen.VolumeBarSkin;
   import com.kingnare.skin.spark.VideoPlayer.normal.FullScreenButtonSkin;
   import com.kingnare.skin.spark.VideoPlayer.normal.PlayPauseButtonSkin;
   import com.kingnare.skin.spark.VideoPlayer.normal.ScrubBarSkin;
   import com.kingnare.skin.spark.VideoPlayer.normal.VolumeBarSkin;
   import flash.accessibility.*;
   import flash.debugger.*;
   import flash.display.*;
   import flash.errors.*;
   import flash.events.*;
   import flash.external.*;
   import flash.geom.*;
   import flash.media.*;
   import flash.net.*;
   import flash.printing.*;
   import flash.profiler.*;
   import flash.system.*;
   import flash.text.*;
   import flash.ui.*;
   import flash.utils.*;
   import flash.xml.*;
   import mx.binding.*;
   import mx.core.DeferredInstanceFromFunction;
   import mx.core.IFlexModuleFactory;
   import mx.core.IStateClient2;
   import mx.core.mx_internal;
   import mx.events.PropertyChangeEvent;
   import mx.filters.*;
   import mx.graphics.GradientEntry;
   import mx.graphics.LinearGradient;
   import mx.graphics.LinearGradientStroke;
   import mx.graphics.SolidColor;
   import mx.graphics.SolidColorStroke;
   import mx.states.AddItems;
   import mx.states.SetProperty;
   import mx.states.SetStyle;
   import mx.states.State;
   import mx.styles.*;
   import spark.components.Button;
   import spark.components.Group;
   import spark.components.Label;
   import spark.components.ToggleButton;
   import spark.components.VideoDisplay;
   import spark.components.VideoPlayer;
   import spark.components.mediaClasses.ScrubBar;
   import spark.components.mediaClasses.VolumeBar;
   import spark.layouts.HorizontalLayout;
   import spark.primitives.Rect;
   import spark.primitives.RectangularDropShadow;
   import spark.skins.SparkSkin;
   
   use namespace mx_internal;
   
   public class VideoPlayer extends SparkSkin implements IBindingClient, IStateClient2
   {
      
      private static var _watcherSetupUtil:IWatcherSetupUtil2;
      
      private static const exclusions:Array = ["videoDisplay","playPauseButton","scrubBar","currentTimeDisplay","timeDivider","durationDisplay","volumeBar","fullScreenButton"];
      
      private var _2084099797_VideoPlayer_Group2:Group;
      
      private var _2084099798_VideoPlayer_Group3:Group;
      
      private var _1046820171_VideoPlayer_Rect1:Rect;
      
      private var _1046820172_VideoPlayer_Rect2:Rect;
      
      public var _VideoPlayer_Rect8:Rect;
      
      private var _149818336_VideoPlayer_SolidColorStroke2:SolidColorStroke;
      
      private var _631637504clippedGroup:Group;
      
      private var _1594163708currentTimeDisplay:Label;
      
      private var _906978543dropShadow:RectangularDropShadow;
      
      private var _775506802durationDisplay:Label;
      
      private var _1661397741fullScreenButton:Button;
      
      private var _1996635380playPauseButton:ToggleButton;
      
      private var _70898871playerControls:Group;
      
      private var _396961948scrubBar:ScrubBar;
      
      private var _1916860532timeDivider:Label;
      
      private var _1524607943videoDisplay:VideoDisplay;
      
      private var _2128310631volumeBar:VolumeBar;
      
      private var __moduleFactoryInitialized:Boolean = false;
      
      mx_internal var _bindings:Array;
      
      mx_internal var _watchers:Array;
      
      mx_internal var _bindingsByDestination:Object;
      
      mx_internal var _bindingsBeginWithWord:Object;
      
      private var _213507019hostComponent:spark.components.VideoPlayer;
      
      public function VideoPlayer()
      {
         var watchers:Array;
         var _VideoPlayer_Rect8_factory:DeferredInstanceFromFunction;
         var _VideoPlayer_RectangularDropShadow1_factory:DeferredInstanceFromFunction;
         var i:uint;
         var bindings:Array = null;
         var target:Object = null;
         var watcherSetupUtilClass:Object = null;
         this.mx_internal::_bindings = [];
         this.mx_internal::_watchers = [];
         this.mx_internal::_bindingsByDestination = {};
         this.mx_internal::_bindingsBeginWithWord = {};
         super();
         mx_internal::_document = this;
         bindings = this._VideoPlayer_bindingsSetup();
         watchers = [];
         target = this;
         if(_watcherSetupUtil == null)
         {
            watcherSetupUtilClass = getDefinitionByName("_com_kingnare_skin_spark_VideoPlayer_VideoPlayerWatcherSetupUtil");
            watcherSetupUtilClass["init"](null);
         }
         _watcherSetupUtil.setup(this,function(param1:String):*
         {
            return target[param1];
         },function(param1:String):*
         {
            return com.kingnare.skin.spark.VideoPlayer.VideoPlayer[param1];
         },bindings,watchers);
         mx_internal::_bindings = mx_internal::_bindings.concat(bindings);
         mx_internal::_watchers = mx_internal::_watchers.concat(watchers);
         this.mxmlContent = [this._VideoPlayer_Group1_i()];
         this.currentState = "uninitialized";
         _VideoPlayer_Rect8_factory = new DeferredInstanceFromFunction(this._VideoPlayer_Rect8_i);
         _VideoPlayer_RectangularDropShadow1_factory = new DeferredInstanceFromFunction(this._VideoPlayer_RectangularDropShadow1_i);
         states = [new State({
            "name":"uninitialized",
            "stateGroups":["normalStates","uninitializedStates"],
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_VideoPlayer_Rect8_factory,
               "destination":"_VideoPlayer_Group2",
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["_VideoPlayer_Group3"]
            }),new AddItems().initializeFromObject({
               "itemsFactory":_VideoPlayer_RectangularDropShadow1_factory,
               "destination":null,
               "propertyName":"mxmlContent",
               "position":"first"
            })]
         }),new State({
            "name":"loading",
            "stateGroups":["normalStates","loadingStates"],
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_VideoPlayer_Rect8_factory,
               "destination":"_VideoPlayer_Group2",
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["_VideoPlayer_Group3"]
            }),new AddItems().initializeFromObject({
               "itemsFactory":_VideoPlayer_RectangularDropShadow1_factory,
               "destination":null,
               "propertyName":"mxmlContent",
               "position":"first"
            }),new SetStyle().initializeFromObject({
               "target":"currentTimeDisplay",
               "name":"color",
               "value":15658734
            }),new SetStyle().initializeFromObject({
               "target":"timeDivider",
               "name":"color",
               "value":15658734
            }),new SetStyle().initializeFromObject({
               "target":"durationDisplay",
               "name":"color",
               "value":15658734
            })]
         }),new State({
            "name":"ready",
            "stateGroups":["normalStates","readyStates"],
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_VideoPlayer_Rect8_factory,
               "destination":"_VideoPlayer_Group2",
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["_VideoPlayer_Group3"]
            }),new AddItems().initializeFromObject({
               "itemsFactory":_VideoPlayer_RectangularDropShadow1_factory,
               "destination":null,
               "propertyName":"mxmlContent",
               "position":"first"
            })]
         }),new State({
            "name":"playing",
            "stateGroups":["normalStates","playingStates"],
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_VideoPlayer_Rect8_factory,
               "destination":"_VideoPlayer_Group2",
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["_VideoPlayer_Group3"]
            }),new AddItems().initializeFromObject({
               "itemsFactory":_VideoPlayer_RectangularDropShadow1_factory,
               "destination":null,
               "propertyName":"mxmlContent",
               "position":"first"
            })]
         }),new State({
            "name":"paused",
            "stateGroups":["normalStates","pausedStates"],
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_VideoPlayer_Rect8_factory,
               "destination":"_VideoPlayer_Group2",
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["_VideoPlayer_Group3"]
            }),new AddItems().initializeFromObject({
               "itemsFactory":_VideoPlayer_RectangularDropShadow1_factory,
               "destination":null,
               "propertyName":"mxmlContent",
               "position":"first"
            })]
         }),new State({
            "name":"buffering",
            "stateGroups":["normalStates","bufferingStates"],
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_VideoPlayer_Rect8_factory,
               "destination":"_VideoPlayer_Group2",
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["_VideoPlayer_Group3"]
            }),new AddItems().initializeFromObject({
               "itemsFactory":_VideoPlayer_RectangularDropShadow1_factory,
               "destination":null,
               "propertyName":"mxmlContent",
               "position":"first"
            })]
         }),new State({
            "name":"playbackError",
            "stateGroups":["normalStates","playbackErrorStates"],
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_VideoPlayer_Rect8_factory,
               "destination":"_VideoPlayer_Group2",
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["_VideoPlayer_Group3"]
            }),new AddItems().initializeFromObject({
               "itemsFactory":_VideoPlayer_RectangularDropShadow1_factory,
               "destination":null,
               "propertyName":"mxmlContent",
               "position":"first"
            })]
         }),new State({
            "name":"disabled",
            "stateGroups":["normalStates","disabledStates"],
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_VideoPlayer_Rect8_factory,
               "destination":"_VideoPlayer_Group2",
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["_VideoPlayer_Group3"]
            }),new AddItems().initializeFromObject({
               "itemsFactory":_VideoPlayer_RectangularDropShadow1_factory,
               "destination":null,
               "propertyName":"mxmlContent",
               "position":"first"
            }),new SetProperty().initializeFromObject({
               "name":"alpha",
               "value":0.5
            })]
         }),new State({
            "name":"uninitializedAndFullScreen",
            "stateGroups":["uninitializedStates","fullScreenStates"],
            "overrides":[new SetStyle().initializeFromObject({
               "name":"chromeColor",
               "value":13421772
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect1",
               "name":"bottom",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect1",
               "name":"left",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect1",
               "name":"right",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect1",
               "name":"top",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"videoDisplay",
               "name":"bottom",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"videoDisplay",
               "name":"left",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"videoDisplay",
               "name":"right",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"videoDisplay",
               "name":"top",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Group3",
               "name":"bottom",
               "value":150
            }),new SetProperty().initializeFromObject({
               "target":"playerControls",
               "name":"maxWidth",
               "value":755
            }),new SetStyle().initializeFromObject({
               "target":"playPauseButton",
               "name":"skinClass",
               "value":com.kingnare.skin.spark.VideoPlayer.fullScreen.PlayPauseButtonSkin
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect2",
               "name":"alpha",
               "value":0.9
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_SolidColorStroke2",
               "name":"color",
               "value":2236962
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_SolidColorStroke2",
               "name":"alpha",
               "value":0.66
            }),new SetStyle().initializeFromObject({
               "target":"scrubBar",
               "name":"skinClass",
               "value":com.kingnare.skin.spark.VideoPlayer.fullScreen.ScrubBarSkin
            }),new SetStyle().initializeFromObject({
               "target":"currentTimeDisplay",
               "name":"color",
               "value":16777215
            }),new SetStyle().initializeFromObject({
               "target":"timeDivider",
               "name":"color",
               "value":16777215
            }),new SetStyle().initializeFromObject({
               "target":"durationDisplay",
               "name":"color",
               "value":16777215
            }),new SetStyle().initializeFromObject({
               "target":"volumeBar",
               "name":"skinClass",
               "value":com.kingnare.skin.spark.VideoPlayer.fullScreen.VolumeBarSkin
            }),new SetStyle().initializeFromObject({
               "target":"fullScreenButton",
               "name":"skinClass",
               "value":com.kingnare.skin.spark.VideoPlayer.fullScreen.FullScreenButtonSkin
            })]
         }),new State({
            "name":"loadingAndFullScreen",
            "stateGroups":["loadingStates","fullScreenStates"],
            "overrides":[new SetStyle().initializeFromObject({
               "name":"chromeColor",
               "value":13421772
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect1",
               "name":"bottom",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect1",
               "name":"left",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect1",
               "name":"right",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect1",
               "name":"top",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"videoDisplay",
               "name":"bottom",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"videoDisplay",
               "name":"left",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"videoDisplay",
               "name":"right",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"videoDisplay",
               "name":"top",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Group3",
               "name":"bottom",
               "value":150
            }),new SetProperty().initializeFromObject({
               "target":"playerControls",
               "name":"maxWidth",
               "value":755
            }),new SetStyle().initializeFromObject({
               "target":"playPauseButton",
               "name":"skinClass",
               "value":com.kingnare.skin.spark.VideoPlayer.fullScreen.PlayPauseButtonSkin
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect2",
               "name":"alpha",
               "value":0.9
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_SolidColorStroke2",
               "name":"color",
               "value":2236962
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_SolidColorStroke2",
               "name":"alpha",
               "value":0.66
            }),new SetStyle().initializeFromObject({
               "target":"scrubBar",
               "name":"skinClass",
               "value":com.kingnare.skin.spark.VideoPlayer.fullScreen.ScrubBarSkin
            }),new SetStyle().initializeFromObject({
               "target":"currentTimeDisplay",
               "name":"color",
               "value":16777215
            }),new SetStyle().initializeFromObject({
               "target":"timeDivider",
               "name":"color",
               "value":16777215
            }),new SetStyle().initializeFromObject({
               "target":"durationDisplay",
               "name":"color",
               "value":16777215
            }),new SetStyle().initializeFromObject({
               "target":"volumeBar",
               "name":"skinClass",
               "value":com.kingnare.skin.spark.VideoPlayer.fullScreen.VolumeBarSkin
            }),new SetStyle().initializeFromObject({
               "target":"fullScreenButton",
               "name":"skinClass",
               "value":com.kingnare.skin.spark.VideoPlayer.fullScreen.FullScreenButtonSkin
            })]
         }),new State({
            "name":"readyAndFullScreen",
            "stateGroups":["readyStates","fullScreenStates"],
            "overrides":[new SetStyle().initializeFromObject({
               "name":"chromeColor",
               "value":13421772
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect1",
               "name":"bottom",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect1",
               "name":"left",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect1",
               "name":"right",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect1",
               "name":"top",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"videoDisplay",
               "name":"bottom",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"videoDisplay",
               "name":"left",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"videoDisplay",
               "name":"right",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"videoDisplay",
               "name":"top",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Group3",
               "name":"bottom",
               "value":150
            }),new SetProperty().initializeFromObject({
               "target":"playerControls",
               "name":"maxWidth",
               "value":755
            }),new SetStyle().initializeFromObject({
               "target":"playPauseButton",
               "name":"skinClass",
               "value":com.kingnare.skin.spark.VideoPlayer.fullScreen.PlayPauseButtonSkin
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect2",
               "name":"alpha",
               "value":0.9
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_SolidColorStroke2",
               "name":"color",
               "value":2236962
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_SolidColorStroke2",
               "name":"alpha",
               "value":0.66
            }),new SetStyle().initializeFromObject({
               "target":"scrubBar",
               "name":"skinClass",
               "value":com.kingnare.skin.spark.VideoPlayer.fullScreen.ScrubBarSkin
            }),new SetStyle().initializeFromObject({
               "target":"currentTimeDisplay",
               "name":"color",
               "value":16777215
            }),new SetStyle().initializeFromObject({
               "target":"timeDivider",
               "name":"color",
               "value":16777215
            }),new SetStyle().initializeFromObject({
               "target":"durationDisplay",
               "name":"color",
               "value":16777215
            }),new SetStyle().initializeFromObject({
               "target":"volumeBar",
               "name":"skinClass",
               "value":com.kingnare.skin.spark.VideoPlayer.fullScreen.VolumeBarSkin
            }),new SetStyle().initializeFromObject({
               "target":"fullScreenButton",
               "name":"skinClass",
               "value":com.kingnare.skin.spark.VideoPlayer.fullScreen.FullScreenButtonSkin
            })]
         }),new State({
            "name":"playingAndFullScreen",
            "stateGroups":["playingStates","fullScreenStates"],
            "overrides":[new SetStyle().initializeFromObject({
               "name":"chromeColor",
               "value":13421772
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect1",
               "name":"bottom",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect1",
               "name":"left",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect1",
               "name":"right",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect1",
               "name":"top",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"videoDisplay",
               "name":"bottom",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"videoDisplay",
               "name":"left",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"videoDisplay",
               "name":"right",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"videoDisplay",
               "name":"top",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Group3",
               "name":"bottom",
               "value":150
            }),new SetProperty().initializeFromObject({
               "target":"playerControls",
               "name":"maxWidth",
               "value":755
            }),new SetStyle().initializeFromObject({
               "target":"playPauseButton",
               "name":"skinClass",
               "value":com.kingnare.skin.spark.VideoPlayer.fullScreen.PlayPauseButtonSkin
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect2",
               "name":"alpha",
               "value":0.9
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_SolidColorStroke2",
               "name":"color",
               "value":2236962
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_SolidColorStroke2",
               "name":"alpha",
               "value":0.66
            }),new SetStyle().initializeFromObject({
               "target":"scrubBar",
               "name":"skinClass",
               "value":com.kingnare.skin.spark.VideoPlayer.fullScreen.ScrubBarSkin
            }),new SetStyle().initializeFromObject({
               "target":"currentTimeDisplay",
               "name":"color",
               "value":16777215
            }),new SetStyle().initializeFromObject({
               "target":"timeDivider",
               "name":"color",
               "value":16777215
            }),new SetStyle().initializeFromObject({
               "target":"durationDisplay",
               "name":"color",
               "value":16777215
            }),new SetStyle().initializeFromObject({
               "target":"volumeBar",
               "name":"skinClass",
               "value":com.kingnare.skin.spark.VideoPlayer.fullScreen.VolumeBarSkin
            }),new SetStyle().initializeFromObject({
               "target":"fullScreenButton",
               "name":"skinClass",
               "value":com.kingnare.skin.spark.VideoPlayer.fullScreen.FullScreenButtonSkin
            })]
         }),new State({
            "name":"pausedAndFullScreen",
            "stateGroups":["pausedStates","fullScreenStates"],
            "overrides":[new SetStyle().initializeFromObject({
               "name":"chromeColor",
               "value":13421772
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect1",
               "name":"bottom",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect1",
               "name":"left",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect1",
               "name":"right",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect1",
               "name":"top",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"videoDisplay",
               "name":"bottom",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"videoDisplay",
               "name":"left",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"videoDisplay",
               "name":"right",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"videoDisplay",
               "name":"top",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Group3",
               "name":"bottom",
               "value":150
            }),new SetProperty().initializeFromObject({
               "target":"playerControls",
               "name":"maxWidth",
               "value":755
            }),new SetStyle().initializeFromObject({
               "target":"playPauseButton",
               "name":"skinClass",
               "value":com.kingnare.skin.spark.VideoPlayer.fullScreen.PlayPauseButtonSkin
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect2",
               "name":"alpha",
               "value":0.9
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_SolidColorStroke2",
               "name":"color",
               "value":2236962
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_SolidColorStroke2",
               "name":"alpha",
               "value":0.66
            }),new SetStyle().initializeFromObject({
               "target":"scrubBar",
               "name":"skinClass",
               "value":com.kingnare.skin.spark.VideoPlayer.fullScreen.ScrubBarSkin
            }),new SetStyle().initializeFromObject({
               "target":"currentTimeDisplay",
               "name":"color",
               "value":16777215
            }),new SetStyle().initializeFromObject({
               "target":"timeDivider",
               "name":"color",
               "value":16777215
            }),new SetStyle().initializeFromObject({
               "target":"durationDisplay",
               "name":"color",
               "value":16777215
            }),new SetStyle().initializeFromObject({
               "target":"volumeBar",
               "name":"skinClass",
               "value":com.kingnare.skin.spark.VideoPlayer.fullScreen.VolumeBarSkin
            }),new SetStyle().initializeFromObject({
               "target":"fullScreenButton",
               "name":"skinClass",
               "value":com.kingnare.skin.spark.VideoPlayer.fullScreen.FullScreenButtonSkin
            })]
         }),new State({
            "name":"bufferingAndFullScreen",
            "stateGroups":["bufferingStates","fullScreenStates"],
            "overrides":[new SetStyle().initializeFromObject({
               "name":"chromeColor",
               "value":13421772
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect1",
               "name":"bottom",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect1",
               "name":"left",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect1",
               "name":"right",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect1",
               "name":"top",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"videoDisplay",
               "name":"bottom",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"videoDisplay",
               "name":"left",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"videoDisplay",
               "name":"right",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"videoDisplay",
               "name":"top",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Group3",
               "name":"bottom",
               "value":150
            }),new SetProperty().initializeFromObject({
               "target":"playerControls",
               "name":"maxWidth",
               "value":755
            }),new SetStyle().initializeFromObject({
               "target":"playPauseButton",
               "name":"skinClass",
               "value":com.kingnare.skin.spark.VideoPlayer.fullScreen.PlayPauseButtonSkin
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect2",
               "name":"alpha",
               "value":0.9
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_SolidColorStroke2",
               "name":"color",
               "value":2236962
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_SolidColorStroke2",
               "name":"alpha",
               "value":0.66
            }),new SetStyle().initializeFromObject({
               "target":"scrubBar",
               "name":"skinClass",
               "value":com.kingnare.skin.spark.VideoPlayer.fullScreen.ScrubBarSkin
            }),new SetStyle().initializeFromObject({
               "target":"currentTimeDisplay",
               "name":"color",
               "value":16777215
            }),new SetStyle().initializeFromObject({
               "target":"timeDivider",
               "name":"color",
               "value":16777215
            }),new SetStyle().initializeFromObject({
               "target":"durationDisplay",
               "name":"color",
               "value":16777215
            }),new SetStyle().initializeFromObject({
               "target":"volumeBar",
               "name":"skinClass",
               "value":com.kingnare.skin.spark.VideoPlayer.fullScreen.VolumeBarSkin
            }),new SetStyle().initializeFromObject({
               "target":"fullScreenButton",
               "name":"skinClass",
               "value":com.kingnare.skin.spark.VideoPlayer.fullScreen.FullScreenButtonSkin
            })]
         }),new State({
            "name":"playbackErrorAndFullScreen",
            "stateGroups":["playbackErrorStates","fullScreenStates"],
            "overrides":[new SetStyle().initializeFromObject({
               "name":"chromeColor",
               "value":13421772
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect1",
               "name":"bottom",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect1",
               "name":"left",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect1",
               "name":"right",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect1",
               "name":"top",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"videoDisplay",
               "name":"bottom",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"videoDisplay",
               "name":"left",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"videoDisplay",
               "name":"right",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"videoDisplay",
               "name":"top",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Group3",
               "name":"bottom",
               "value":150
            }),new SetProperty().initializeFromObject({
               "target":"playerControls",
               "name":"maxWidth",
               "value":755
            }),new SetStyle().initializeFromObject({
               "target":"playPauseButton",
               "name":"skinClass",
               "value":com.kingnare.skin.spark.VideoPlayer.fullScreen.PlayPauseButtonSkin
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect2",
               "name":"alpha",
               "value":0.9
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_SolidColorStroke2",
               "name":"color",
               "value":2236962
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_SolidColorStroke2",
               "name":"alpha",
               "value":0.66
            }),new SetStyle().initializeFromObject({
               "target":"scrubBar",
               "name":"skinClass",
               "value":com.kingnare.skin.spark.VideoPlayer.fullScreen.ScrubBarSkin
            }),new SetStyle().initializeFromObject({
               "target":"currentTimeDisplay",
               "name":"color",
               "value":16777215
            }),new SetStyle().initializeFromObject({
               "target":"timeDivider",
               "name":"color",
               "value":16777215
            }),new SetStyle().initializeFromObject({
               "target":"durationDisplay",
               "name":"color",
               "value":16777215
            }),new SetStyle().initializeFromObject({
               "target":"volumeBar",
               "name":"skinClass",
               "value":com.kingnare.skin.spark.VideoPlayer.fullScreen.VolumeBarSkin
            }),new SetStyle().initializeFromObject({
               "target":"fullScreenButton",
               "name":"skinClass",
               "value":com.kingnare.skin.spark.VideoPlayer.fullScreen.FullScreenButtonSkin
            })]
         }),new State({
            "name":"disabledAndFullScreen",
            "stateGroups":["disabledStates","fullScreenStates"],
            "overrides":[new SetProperty().initializeFromObject({
               "name":"alpha",
               "value":0.5
            }),new SetStyle().initializeFromObject({
               "name":"chromeColor",
               "value":13421772
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect1",
               "name":"bottom",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect1",
               "name":"left",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect1",
               "name":"right",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect1",
               "name":"top",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"videoDisplay",
               "name":"bottom",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"videoDisplay",
               "name":"left",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"videoDisplay",
               "name":"right",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"videoDisplay",
               "name":"top",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Group3",
               "name":"bottom",
               "value":150
            }),new SetProperty().initializeFromObject({
               "target":"playerControls",
               "name":"maxWidth",
               "value":755
            }),new SetStyle().initializeFromObject({
               "target":"playPauseButton",
               "name":"skinClass",
               "value":com.kingnare.skin.spark.VideoPlayer.fullScreen.PlayPauseButtonSkin
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_Rect2",
               "name":"alpha",
               "value":0.9
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_SolidColorStroke2",
               "name":"color",
               "value":2236962
            }),new SetProperty().initializeFromObject({
               "target":"_VideoPlayer_SolidColorStroke2",
               "name":"alpha",
               "value":0.66
            }),new SetStyle().initializeFromObject({
               "target":"scrubBar",
               "name":"skinClass",
               "value":com.kingnare.skin.spark.VideoPlayer.fullScreen.ScrubBarSkin
            }),new SetStyle().initializeFromObject({
               "target":"currentTimeDisplay",
               "name":"color",
               "value":16777215
            }),new SetStyle().initializeFromObject({
               "target":"timeDivider",
               "name":"color",
               "value":16777215
            }),new SetStyle().initializeFromObject({
               "target":"durationDisplay",
               "name":"color",
               "value":16777215
            }),new SetStyle().initializeFromObject({
               "target":"volumeBar",
               "name":"skinClass",
               "value":com.kingnare.skin.spark.VideoPlayer.fullScreen.VolumeBarSkin
            }),new SetStyle().initializeFromObject({
               "target":"fullScreenButton",
               "name":"skinClass",
               "value":com.kingnare.skin.spark.VideoPlayer.fullScreen.FullScreenButtonSkin
            })]
         })];
         i = 0;
         while(i < bindings.length)
         {
            Binding(bindings[i]).execute();
            i++;
         }
      }
      
      public static function set watcherSetupUtil(param1:IWatcherSetupUtil2) : void
      {
         com.kingnare.skin.spark.VideoPlayer.VideoPlayer._watcherSetupUtil = param1;
      }
      
      override public function set moduleFactory(param1:IFlexModuleFactory) : void
      {
         super.moduleFactory = param1;
         if(this.__moduleFactoryInitialized)
         {
            return;
         }
         this.__moduleFactoryInitialized = true;
      }
      
      override public function initialize() : void
      {
         super.initialize();
      }
      
      override protected function initializationComplete() : void
      {
         useChromeColor = true;
         super.initializationComplete();
      }
      
      override public function get colorizeExclusions() : Array
      {
         return exclusions;
      }
      
      override protected function updateDisplayList(param1:Number, param2:Number) : void
      {
         this.dropShadow.visible = getStyle("dropShadowVisible");
         super.updateDisplayList(param1,param2);
      }
      
      private function _VideoPlayer_RectangularDropShadow1_i() : RectangularDropShadow
      {
         var _loc1_:RectangularDropShadow = new RectangularDropShadow();
         _loc1_.blurX = 17;
         _loc1_.blurY = 17;
         _loc1_.alpha = 0.32;
         _loc1_.distance = 4;
         _loc1_.angle = 90;
         _loc1_.color = 1250067;
         _loc1_.left = 0;
         _loc1_.top = 0;
         _loc1_.right = 0;
         _loc1_.bottom = 0;
         _loc1_.id = "dropShadow";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         this.dropShadow = _loc1_;
         BindingManager.executeBindings(this,"dropShadow",this.dropShadow);
         return _loc1_;
      }
      
      private function _VideoPlayer_Group1_i() : Group
      {
         var _loc1_:Group = new Group();
         _loc1_.clipAndEnableScrolling = true;
         _loc1_.left = 0;
         _loc1_.top = 0;
         _loc1_.right = 0;
         _loc1_.bottom = 0;
         _loc1_.mxmlContent = [this._VideoPlayer_Group2_i()];
         _loc1_.id = "clippedGroup";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         this.clippedGroup = _loc1_;
         BindingManager.executeBindings(this,"clippedGroup",this.clippedGroup);
         return _loc1_;
      }
      
      private function _VideoPlayer_Group2_i() : Group
      {
         var _loc1_:Group = new Group();
         _loc1_.minWidth = 263;
         _loc1_.minHeight = 184;
         _loc1_.left = 0;
         _loc1_.right = 0;
         _loc1_.top = 0;
         _loc1_.bottom = 0;
         _loc1_.mxmlContent = [this._VideoPlayer_Rect1_i(),this._VideoPlayer_VideoDisplay1_i(),this._VideoPlayer_Group3_i()];
         _loc1_.id = "_VideoPlayer_Group2";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         this._VideoPlayer_Group2 = _loc1_;
         BindingManager.executeBindings(this,"_VideoPlayer_Group2",this._VideoPlayer_Group2);
         return _loc1_;
      }
      
      private function _VideoPlayer_Rect1_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.bottom = 1;
         _loc1_.left = 1;
         _loc1_.right = 1;
         _loc1_.top = 1;
         _loc1_.fill = this._VideoPlayer_SolidColor1_c();
         _loc1_.initialized(this,"_VideoPlayer_Rect1");
         this._VideoPlayer_Rect1 = _loc1_;
         BindingManager.executeBindings(this,"_VideoPlayer_Rect1",this._VideoPlayer_Rect1);
         return _loc1_;
      }
      
      private function _VideoPlayer_SolidColor1_c() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 0;
         return _loc1_;
      }
      
      private function _VideoPlayer_VideoDisplay1_i() : VideoDisplay
      {
         var _loc1_:VideoDisplay = new VideoDisplay();
         _loc1_.bottom = 24;
         _loc1_.left = 1;
         _loc1_.right = 1;
         _loc1_.top = 1;
         _loc1_.id = "videoDisplay";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         this.videoDisplay = _loc1_;
         BindingManager.executeBindings(this,"videoDisplay",this.videoDisplay);
         return _loc1_;
      }
      
      private function _VideoPlayer_Group3_i() : Group
      {
         var _loc1_:Group = new Group();
         _loc1_.left = 0;
         _loc1_.right = 0;
         _loc1_.height = 24;
         _loc1_.bottom = 0;
         _loc1_.mxmlContent = [this._VideoPlayer_Group4_i()];
         _loc1_.id = "_VideoPlayer_Group3";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         this._VideoPlayer_Group3 = _loc1_;
         BindingManager.executeBindings(this,"_VideoPlayer_Group3",this._VideoPlayer_Group3);
         return _loc1_;
      }
      
      private function _VideoPlayer_Group4_i() : Group
      {
         var _loc1_:Group = new Group();
         _loc1_.bottom = 0;
         _loc1_.horizontalCenter = 0;
         _loc1_.left = 0;
         _loc1_.right = 0;
         _loc1_.mxmlContent = [this._VideoPlayer_ToggleButton1_i(),this._VideoPlayer_Group5_c(),this._VideoPlayer_VolumeBar1_i(),this._VideoPlayer_Button1_i()];
         _loc1_.id = "playerControls";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         this.playerControls = _loc1_;
         BindingManager.executeBindings(this,"playerControls",this.playerControls);
         return _loc1_;
      }
      
      private function _VideoPlayer_ToggleButton1_i() : ToggleButton
      {
         var _loc1_:ToggleButton = new ToggleButton();
         _loc1_.left = 0;
         _loc1_.bottom = 0;
         _loc1_.width = 39;
         _loc1_.layoutDirection = "ltr";
         _loc1_.setStyle("skinClass",com.kingnare.skin.spark.VideoPlayer.normal.PlayPauseButtonSkin);
         _loc1_.id = "playPauseButton";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         this.playPauseButton = _loc1_;
         BindingManager.executeBindings(this,"playPauseButton",this.playPauseButton);
         return _loc1_;
      }
      
      private function _VideoPlayer_Group5_c() : Group
      {
         var _loc1_:Group = new Group();
         _loc1_.left = 38;
         _loc1_.right = 75;
         _loc1_.top = 0;
         _loc1_.bottom = 0;
         _loc1_.mxmlContent = [this._VideoPlayer_Rect2_i(),this._VideoPlayer_Rect3_c(),this._VideoPlayer_Rect4_c(),this._VideoPlayer_Group6_c()];
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         return _loc1_;
      }
      
      private function _VideoPlayer_Rect2_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 0;
         _loc1_.right = 0;
         _loc1_.top = 0;
         _loc1_.bottom = 0;
         _loc1_.fill = this._VideoPlayer_LinearGradient1_c();
         _loc1_.stroke = this._VideoPlayer_SolidColorStroke1_c();
         _loc1_.initialized(this,"_VideoPlayer_Rect2");
         this._VideoPlayer_Rect2 = _loc1_;
         BindingManager.executeBindings(this,"_VideoPlayer_Rect2",this._VideoPlayer_Rect2);
         return _loc1_;
      }
      
      private function _VideoPlayer_LinearGradient1_c() : LinearGradient
      {
         var _loc1_:LinearGradient = new LinearGradient();
         _loc1_.rotation = 90;
         _loc1_.entries = [this._VideoPlayer_GradientEntry1_c(),this._VideoPlayer_GradientEntry2_c()];
         return _loc1_;
      }
      
      private function _VideoPlayer_GradientEntry1_c() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 5855577;
         return _loc1_;
      }
      
      private function _VideoPlayer_GradientEntry2_c() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 4802889;
         return _loc1_;
      }
      
      private function _VideoPlayer_SolidColorStroke1_c() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.caps = "none";
         _loc1_.color = 0;
         _loc1_.joints = "miter";
         _loc1_.miterLimit = 4;
         _loc1_.weight = 1;
         return _loc1_;
      }
      
      private function _VideoPlayer_Rect3_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 1;
         _loc1_.right = 1;
         _loc1_.top = 1;
         _loc1_.bottom = 1;
         _loc1_.stroke = this._VideoPlayer_LinearGradientStroke1_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _VideoPlayer_LinearGradientStroke1_c() : LinearGradientStroke
      {
         var _loc1_:LinearGradientStroke = new LinearGradientStroke();
         _loc1_.rotation = 90;
         _loc1_.joints = "miter";
         _loc1_.miterLimit = 4;
         _loc1_.weight = 1;
         _loc1_.entries = [this._VideoPlayer_GradientEntry3_c(),this._VideoPlayer_GradientEntry4_c()];
         return _loc1_;
      }
      
      private function _VideoPlayer_GradientEntry3_c() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 6710886;
         return _loc1_;
      }
      
      private function _VideoPlayer_GradientEntry4_c() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 5066061;
         return _loc1_;
      }
      
      private function _VideoPlayer_Rect4_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 0;
         _loc1_.right = 0;
         _loc1_.top = 0;
         _loc1_.bottom = 0;
         _loc1_.stroke = this._VideoPlayer_SolidColorStroke2_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _VideoPlayer_SolidColorStroke2_i() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.color = 1250067;
         this._VideoPlayer_SolidColorStroke2 = _loc1_;
         BindingManager.executeBindings(this,"_VideoPlayer_SolidColorStroke2",this._VideoPlayer_SolidColorStroke2);
         return _loc1_;
      }
      
      private function _VideoPlayer_Group6_c() : Group
      {
         var _loc1_:Group = new Group();
         _loc1_.left = 0;
         _loc1_.right = 0;
         _loc1_.height = 23;
         _loc1_.bottom = 0;
         _loc1_.layout = this._VideoPlayer_HorizontalLayout1_c();
         _loc1_.mxmlContent = [this._VideoPlayer_Rect5_c(),this._VideoPlayer_ScrubBar1_i(),this._VideoPlayer_Rect6_c(),this._VideoPlayer_Label1_i(),this._VideoPlayer_Label2_i(),this._VideoPlayer_Label3_i(),this._VideoPlayer_Rect7_c()];
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         return _loc1_;
      }
      
      private function _VideoPlayer_HorizontalLayout1_c() : HorizontalLayout
      {
         var _loc1_:HorizontalLayout = new HorizontalLayout();
         _loc1_.verticalAlign = "middle";
         _loc1_.gap = 1;
         return _loc1_;
      }
      
      private function _VideoPlayer_Rect5_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.width = 7;
         _loc1_.height = 1;
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _VideoPlayer_ScrubBar1_i() : ScrubBar
      {
         var _loc1_:ScrubBar = new ScrubBar();
         _loc1_.percentWidth = 100;
         _loc1_.setStyle("liveDragging",true);
         _loc1_.setStyle("skinClass",com.kingnare.skin.spark.VideoPlayer.normal.ScrubBarSkin);
         _loc1_.id = "scrubBar";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         this.scrubBar = _loc1_;
         BindingManager.executeBindings(this,"scrubBar",this.scrubBar);
         return _loc1_;
      }
      
      private function _VideoPlayer_Rect6_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.width = 8;
         _loc1_.height = 1;
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _VideoPlayer_Label1_i() : Label
      {
         var _loc1_:Label = new Label();
         _loc1_.id = "currentTimeDisplay";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         this.currentTimeDisplay = _loc1_;
         BindingManager.executeBindings(this,"currentTimeDisplay",this.currentTimeDisplay);
         return _loc1_;
      }
      
      private function _VideoPlayer_Label2_i() : Label
      {
         var _loc1_:Label = new Label();
         _loc1_.text = "/";
         _loc1_.id = "timeDivider";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         this.timeDivider = _loc1_;
         BindingManager.executeBindings(this,"timeDivider",this.timeDivider);
         return _loc1_;
      }
      
      private function _VideoPlayer_Label3_i() : Label
      {
         var _loc1_:Label = new Label();
         _loc1_.id = "durationDisplay";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         this.durationDisplay = _loc1_;
         BindingManager.executeBindings(this,"durationDisplay",this.durationDisplay);
         return _loc1_;
      }
      
      private function _VideoPlayer_Rect7_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.width = 8;
         _loc1_.height = 1;
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _VideoPlayer_VolumeBar1_i() : VolumeBar
      {
         var _loc1_:VolumeBar = new VolumeBar();
         _loc1_.snapInterval = 0.01;
         _loc1_.stepSize = 0.01;
         _loc1_.right = 37;
         _loc1_.bottom = 0;
         _loc1_.layoutDirection = "ltr";
         _loc1_.setStyle("liveDragging",true);
         _loc1_.setStyle("skinClass",com.kingnare.skin.spark.VideoPlayer.normal.VolumeBarSkin);
         _loc1_.id = "volumeBar";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         this.volumeBar = _loc1_;
         BindingManager.executeBindings(this,"volumeBar",this.volumeBar);
         return _loc1_;
      }
      
      private function _VideoPlayer_Button1_i() : Button
      {
         var _loc1_:Button = new Button();
         _loc1_.right = 0;
         _loc1_.bottom = 0;
         _loc1_.label = "Fullscreen";
         _loc1_.setStyle("skinClass",com.kingnare.skin.spark.VideoPlayer.normal.FullScreenButtonSkin);
         _loc1_.id = "fullScreenButton";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         this.fullScreenButton = _loc1_;
         BindingManager.executeBindings(this,"fullScreenButton",this.fullScreenButton);
         return _loc1_;
      }
      
      private function _VideoPlayer_Rect8_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 0;
         _loc1_.right = 0;
         _loc1_.top = 0;
         _loc1_.bottom = 0;
         _loc1_.stroke = this._VideoPlayer_SolidColorStroke3_c();
         _loc1_.initialized(this,"_VideoPlayer_Rect8");
         this._VideoPlayer_Rect8 = _loc1_;
         BindingManager.executeBindings(this,"_VideoPlayer_Rect8",this._VideoPlayer_Rect8);
         return _loc1_;
      }
      
      private function _VideoPlayer_SolidColorStroke3_c() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.color = 1250067;
         return _loc1_;
      }
      
      private function _VideoPlayer_bindingsSetup() : Array
      {
         var result:Array = [];
         result[0] = new Binding(this,function():Number
         {
            return Math.max(0,184 - clippedGroup.height);
         },null,"clippedGroup.verticalScrollPosition");
         return result;
      }
      
      [Bindable(event="propertyChange")]
      public function get _VideoPlayer_Group2() : Group
      {
         return this._2084099797_VideoPlayer_Group2;
      }
      
      public function set _VideoPlayer_Group2(param1:Group) : void
      {
         var _loc2_:Object = this._2084099797_VideoPlayer_Group2;
         if(_loc2_ !== param1)
         {
            this._2084099797_VideoPlayer_Group2 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_VideoPlayer_Group2",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _VideoPlayer_Group3() : Group
      {
         return this._2084099798_VideoPlayer_Group3;
      }
      
      public function set _VideoPlayer_Group3(param1:Group) : void
      {
         var _loc2_:Object = this._2084099798_VideoPlayer_Group3;
         if(_loc2_ !== param1)
         {
            this._2084099798_VideoPlayer_Group3 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_VideoPlayer_Group3",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _VideoPlayer_Rect1() : Rect
      {
         return this._1046820171_VideoPlayer_Rect1;
      }
      
      public function set _VideoPlayer_Rect1(param1:Rect) : void
      {
         var _loc2_:Object = this._1046820171_VideoPlayer_Rect1;
         if(_loc2_ !== param1)
         {
            this._1046820171_VideoPlayer_Rect1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_VideoPlayer_Rect1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _VideoPlayer_Rect2() : Rect
      {
         return this._1046820172_VideoPlayer_Rect2;
      }
      
      public function set _VideoPlayer_Rect2(param1:Rect) : void
      {
         var _loc2_:Object = this._1046820172_VideoPlayer_Rect2;
         if(_loc2_ !== param1)
         {
            this._1046820172_VideoPlayer_Rect2 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_VideoPlayer_Rect2",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _VideoPlayer_SolidColorStroke2() : SolidColorStroke
      {
         return this._149818336_VideoPlayer_SolidColorStroke2;
      }
      
      public function set _VideoPlayer_SolidColorStroke2(param1:SolidColorStroke) : void
      {
         var _loc2_:Object = this._149818336_VideoPlayer_SolidColorStroke2;
         if(_loc2_ !== param1)
         {
            this._149818336_VideoPlayer_SolidColorStroke2 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_VideoPlayer_SolidColorStroke2",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get clippedGroup() : Group
      {
         return this._631637504clippedGroup;
      }
      
      public function set clippedGroup(param1:Group) : void
      {
         var _loc2_:Object = this._631637504clippedGroup;
         if(_loc2_ !== param1)
         {
            this._631637504clippedGroup = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"clippedGroup",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get currentTimeDisplay() : Label
      {
         return this._1594163708currentTimeDisplay;
      }
      
      public function set currentTimeDisplay(param1:Label) : void
      {
         var _loc2_:Object = this._1594163708currentTimeDisplay;
         if(_loc2_ !== param1)
         {
            this._1594163708currentTimeDisplay = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"currentTimeDisplay",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get dropShadow() : RectangularDropShadow
      {
         return this._906978543dropShadow;
      }
      
      public function set dropShadow(param1:RectangularDropShadow) : void
      {
         var _loc2_:Object = this._906978543dropShadow;
         if(_loc2_ !== param1)
         {
            this._906978543dropShadow = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"dropShadow",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get durationDisplay() : Label
      {
         return this._775506802durationDisplay;
      }
      
      public function set durationDisplay(param1:Label) : void
      {
         var _loc2_:Object = this._775506802durationDisplay;
         if(_loc2_ !== param1)
         {
            this._775506802durationDisplay = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"durationDisplay",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get fullScreenButton() : Button
      {
         return this._1661397741fullScreenButton;
      }
      
      public function set fullScreenButton(param1:Button) : void
      {
         var _loc2_:Object = this._1661397741fullScreenButton;
         if(_loc2_ !== param1)
         {
            this._1661397741fullScreenButton = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"fullScreenButton",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get playPauseButton() : ToggleButton
      {
         return this._1996635380playPauseButton;
      }
      
      public function set playPauseButton(param1:ToggleButton) : void
      {
         var _loc2_:Object = this._1996635380playPauseButton;
         if(_loc2_ !== param1)
         {
            this._1996635380playPauseButton = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"playPauseButton",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get playerControls() : Group
      {
         return this._70898871playerControls;
      }
      
      public function set playerControls(param1:Group) : void
      {
         var _loc2_:Object = this._70898871playerControls;
         if(_loc2_ !== param1)
         {
            this._70898871playerControls = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"playerControls",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get scrubBar() : ScrubBar
      {
         return this._396961948scrubBar;
      }
      
      public function set scrubBar(param1:ScrubBar) : void
      {
         var _loc2_:Object = this._396961948scrubBar;
         if(_loc2_ !== param1)
         {
            this._396961948scrubBar = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"scrubBar",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get timeDivider() : Label
      {
         return this._1916860532timeDivider;
      }
      
      public function set timeDivider(param1:Label) : void
      {
         var _loc2_:Object = this._1916860532timeDivider;
         if(_loc2_ !== param1)
         {
            this._1916860532timeDivider = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"timeDivider",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get videoDisplay() : VideoDisplay
      {
         return this._1524607943videoDisplay;
      }
      
      public function set videoDisplay(param1:VideoDisplay) : void
      {
         var _loc2_:Object = this._1524607943videoDisplay;
         if(_loc2_ !== param1)
         {
            this._1524607943videoDisplay = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"videoDisplay",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get volumeBar() : VolumeBar
      {
         return this._2128310631volumeBar;
      }
      
      public function set volumeBar(param1:VolumeBar) : void
      {
         var _loc2_:Object = this._2128310631volumeBar;
         if(_loc2_ !== param1)
         {
            this._2128310631volumeBar = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"volumeBar",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get hostComponent() : spark.components.VideoPlayer
      {
         return this._213507019hostComponent;
      }
      
      public function set hostComponent(param1:spark.components.VideoPlayer) : void
      {
         var _loc2_:Object = this._213507019hostComponent;
         if(_loc2_ !== param1)
         {
            this._213507019hostComponent = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"hostComponent",_loc2_,param1));
            }
         }
      }
   }
}

