package com.kingnare.skin.spark.VideoPlayer.normal
{
   import mx.binding.BindingManager;
   import mx.core.DeferredInstanceFromFunction;
   import mx.core.IFlexModuleFactory;
   import mx.core.IStateClient2;
   import mx.events.PropertyChangeEvent;
   import mx.graphics.GradientEntry;
   import mx.graphics.LinearGradient;
   import mx.graphics.LinearGradientStroke;
   import mx.graphics.SolidColor;
   import mx.graphics.SolidColorStroke;
   import mx.states.AddItems;
   import mx.states.SetProperty;
   import mx.states.State;
   import spark.components.Button;
   import spark.primitives.Rect;
   import spark.skins.SparkSkin;
   
   public class VolumeBarThumbSkin extends SparkSkin implements IStateClient2
   {
      
      private var _1444641237_VolumeBarThumbSkin_GradientEntry1:GradientEntry;
      
      private var _1444641238_VolumeBarThumbSkin_GradientEntry2:GradientEntry;
      
      private var _1444641239_VolumeBarThumbSkin_GradientEntry3:GradientEntry;
      
      private var _1444641240_VolumeBarThumbSkin_GradientEntry4:GradientEntry;
      
      public var _VolumeBarThumbSkin_Rect2:Rect;
      
      public var _VolumeBarThumbSkin_Rect3:Rect;
      
      private var _728661292fillbase:Rect;
      
      private var __moduleFactoryInitialized:Boolean = false;
      
      private var _213507019hostComponent:Button;
      
      public function VolumeBarThumbSkin()
      {
         super();
         mx_internal::_document = this;
         this.mxmlContent = [this._VolumeBarThumbSkin_Rect1_i(),this._VolumeBarThumbSkin_Rect2_i(),this._VolumeBarThumbSkin_Rect4_c()];
         this.currentState = "up";
         var _loc1_:DeferredInstanceFromFunction = new DeferredInstanceFromFunction(this._VolumeBarThumbSkin_Rect3_i);
         states = [new State({
            "name":"up",
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_loc1_,
               "destination":null,
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["_VolumeBarThumbSkin_Rect2"]
            })]
         }),new State({
            "name":"over",
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_loc1_,
               "destination":null,
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["_VolumeBarThumbSkin_Rect2"]
            }),new SetProperty().initializeFromObject({
               "target":"_VolumeBarThumbSkin_GradientEntry1",
               "name":"alpha",
               "value":0.5
            }),new SetProperty().initializeFromObject({
               "target":"_VolumeBarThumbSkin_GradientEntry2",
               "name":"alpha",
               "value":0.3
            })]
         }),new State({
            "name":"down",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_VolumeBarThumbSkin_GradientEntry1",
               "name":"alpha",
               "value":0.01
            }),new SetProperty().initializeFromObject({
               "target":"_VolumeBarThumbSkin_GradientEntry2",
               "name":"alpha",
               "value":0.01
            }),new SetProperty().initializeFromObject({
               "target":"_VolumeBarThumbSkin_GradientEntry3",
               "name":"alpha",
               "value":0.2
            }),new SetProperty().initializeFromObject({
               "target":"_VolumeBarThumbSkin_GradientEntry3",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_VolumeBarThumbSkin_GradientEntry4",
               "name":"alpha",
               "value":0.15
            }),new SetProperty().initializeFromObject({
               "target":"_VolumeBarThumbSkin_GradientEntry4",
               "name":"color",
               "value":0
            })]
         }),new State({
            "name":"disabled",
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_loc1_,
               "destination":null,
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["_VolumeBarThumbSkin_Rect2"]
            })]
         })];
      }
      
      override public function set moduleFactory(param1:IFlexModuleFactory) : void
      {
         super.moduleFactory = param1;
         if(this.__moduleFactoryInitialized)
         {
            return;
         }
         this.__moduleFactoryInitialized = true;
      }
      
      override public function initialize() : void
      {
         super.initialize();
      }
      
      override protected function initializationComplete() : void
      {
         useChromeColor = true;
         super.initializationComplete();
      }
      
      private function _VolumeBarThumbSkin_Rect1_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 0;
         _loc1_.right = 0;
         _loc1_.top = 0;
         _loc1_.bottom = 0;
         _loc1_.radiusX = 8;
         _loc1_.radiusY = 8;
         _loc1_.fill = this._VolumeBarThumbSkin_SolidColor1_c();
         _loc1_.initialized(this,"fillbase");
         this.fillbase = _loc1_;
         BindingManager.executeBindings(this,"fillbase",this.fillbase);
         return _loc1_;
      }
      
      private function _VolumeBarThumbSkin_SolidColor1_c() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 3355443;
         return _loc1_;
      }
      
      private function _VolumeBarThumbSkin_Rect2_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 1;
         _loc1_.right = 1;
         _loc1_.top = 1;
         _loc1_.bottom = 1;
         _loc1_.radiusX = 8;
         _loc1_.radiusY = 8;
         _loc1_.fill = this._VolumeBarThumbSkin_LinearGradient1_c();
         _loc1_.initialized(this,"_VolumeBarThumbSkin_Rect2");
         this._VolumeBarThumbSkin_Rect2 = _loc1_;
         BindingManager.executeBindings(this,"_VolumeBarThumbSkin_Rect2",this._VolumeBarThumbSkin_Rect2);
         return _loc1_;
      }
      
      private function _VolumeBarThumbSkin_LinearGradient1_c() : LinearGradient
      {
         var _loc1_:LinearGradient = new LinearGradient();
         _loc1_.rotation = 90;
         _loc1_.entries = [this._VolumeBarThumbSkin_GradientEntry1_i(),this._VolumeBarThumbSkin_GradientEntry2_i()];
         return _loc1_;
      }
      
      private function _VolumeBarThumbSkin_GradientEntry1_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 16777215;
         _loc1_.alpha = 0.35;
         this._VolumeBarThumbSkin_GradientEntry1 = _loc1_;
         BindingManager.executeBindings(this,"_VolumeBarThumbSkin_GradientEntry1",this._VolumeBarThumbSkin_GradientEntry1);
         return _loc1_;
      }
      
      private function _VolumeBarThumbSkin_GradientEntry2_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 16777215;
         _loc1_.alpha = 0.15;
         this._VolumeBarThumbSkin_GradientEntry2 = _loc1_;
         BindingManager.executeBindings(this,"_VolumeBarThumbSkin_GradientEntry2",this._VolumeBarThumbSkin_GradientEntry2);
         return _loc1_;
      }
      
      private function _VolumeBarThumbSkin_Rect3_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 1;
         _loc1_.right = 1;
         _loc1_.top = 1;
         _loc1_.bottom = 1;
         _loc1_.radiusX = 8;
         _loc1_.radiusY = 8;
         _loc1_.stroke = this._VolumeBarThumbSkin_LinearGradientStroke1_c();
         _loc1_.initialized(this,"_VolumeBarThumbSkin_Rect3");
         this._VolumeBarThumbSkin_Rect3 = _loc1_;
         BindingManager.executeBindings(this,"_VolumeBarThumbSkin_Rect3",this._VolumeBarThumbSkin_Rect3);
         return _loc1_;
      }
      
      private function _VolumeBarThumbSkin_LinearGradientStroke1_c() : LinearGradientStroke
      {
         var _loc1_:LinearGradientStroke = new LinearGradientStroke();
         _loc1_.rotation = 90;
         _loc1_.weight = 1;
         _loc1_.entries = [this._VolumeBarThumbSkin_GradientEntry3_i(),this._VolumeBarThumbSkin_GradientEntry4_i()];
         return _loc1_;
      }
      
      private function _VolumeBarThumbSkin_GradientEntry3_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 16777215;
         _loc1_.alpha = 0.1;
         this._VolumeBarThumbSkin_GradientEntry3 = _loc1_;
         BindingManager.executeBindings(this,"_VolumeBarThumbSkin_GradientEntry3",this._VolumeBarThumbSkin_GradientEntry3);
         return _loc1_;
      }
      
      private function _VolumeBarThumbSkin_GradientEntry4_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 16777215;
         _loc1_.alpha = 0.03;
         this._VolumeBarThumbSkin_GradientEntry4 = _loc1_;
         BindingManager.executeBindings(this,"_VolumeBarThumbSkin_GradientEntry4",this._VolumeBarThumbSkin_GradientEntry4);
         return _loc1_;
      }
      
      private function _VolumeBarThumbSkin_Rect4_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 0;
         _loc1_.right = 0;
         _loc1_.top = 0;
         _loc1_.bottom = 0;
         _loc1_.radiusX = 8;
         _loc1_.radiusY = 8;
         _loc1_.stroke = this._VolumeBarThumbSkin_SolidColorStroke1_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _VolumeBarThumbSkin_SolidColorStroke1_c() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.color = 0;
         return _loc1_;
      }
      
      [Bindable(event="propertyChange")]
      public function get _VolumeBarThumbSkin_GradientEntry1() : GradientEntry
      {
         return this._1444641237_VolumeBarThumbSkin_GradientEntry1;
      }
      
      public function set _VolumeBarThumbSkin_GradientEntry1(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._1444641237_VolumeBarThumbSkin_GradientEntry1;
         if(_loc2_ !== param1)
         {
            this._1444641237_VolumeBarThumbSkin_GradientEntry1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_VolumeBarThumbSkin_GradientEntry1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _VolumeBarThumbSkin_GradientEntry2() : GradientEntry
      {
         return this._1444641238_VolumeBarThumbSkin_GradientEntry2;
      }
      
      public function set _VolumeBarThumbSkin_GradientEntry2(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._1444641238_VolumeBarThumbSkin_GradientEntry2;
         if(_loc2_ !== param1)
         {
            this._1444641238_VolumeBarThumbSkin_GradientEntry2 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_VolumeBarThumbSkin_GradientEntry2",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _VolumeBarThumbSkin_GradientEntry3() : GradientEntry
      {
         return this._1444641239_VolumeBarThumbSkin_GradientEntry3;
      }
      
      public function set _VolumeBarThumbSkin_GradientEntry3(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._1444641239_VolumeBarThumbSkin_GradientEntry3;
         if(_loc2_ !== param1)
         {
            this._1444641239_VolumeBarThumbSkin_GradientEntry3 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_VolumeBarThumbSkin_GradientEntry3",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _VolumeBarThumbSkin_GradientEntry4() : GradientEntry
      {
         return this._1444641240_VolumeBarThumbSkin_GradientEntry4;
      }
      
      public function set _VolumeBarThumbSkin_GradientEntry4(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._1444641240_VolumeBarThumbSkin_GradientEntry4;
         if(_loc2_ !== param1)
         {
            this._1444641240_VolumeBarThumbSkin_GradientEntry4 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_VolumeBarThumbSkin_GradientEntry4",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get fillbase() : Rect
      {
         return this._728661292fillbase;
      }
      
      public function set fillbase(param1:Rect) : void
      {
         var _loc2_:Object = this._728661292fillbase;
         if(_loc2_ !== param1)
         {
            this._728661292fillbase = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"fillbase",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get hostComponent() : Button
      {
         return this._213507019hostComponent;
      }
      
      public function set hostComponent(param1:Button) : void
      {
         var _loc2_:Object = this._213507019hostComponent;
         if(_loc2_ !== param1)
         {
            this._213507019hostComponent = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"hostComponent",_loc2_,param1));
            }
         }
      }
   }
}

