package com.kingnare.skin.mx.PopUpButton
{
   import com.kingnare.skin.mx.util.DrawUtil;
   import flash.display.Graphics;
   import mx.skins.Border;
   
   public class PopUpButtonSkin extends Border
   {
      
      public function PopUpButtonSkin()
      {
         super();
      }
      
      override public function get measuredWidth() : Number
      {
         return 22;
      }
      
      override public function get measuredHeight() : Number
      {
         return 22;
      }
      
      override protected function updateDisplayList(param1:Number, param2:Number) : void
      {
         var _loc3_:Graphics = graphics;
         _loc3_.clear();
         var _loc4_:Number = param1;
         var _loc5_:Number = param2;
         var _loc6_:Array = [0.1,0.1];
         var _loc7_:Array = [16777215,16777215];
         var _loc8_:Array = [0.08,0.03];
         var _loc9_:Array = [8421504,8421504];
         switch(name)
         {
            case "upSkin":
               break;
            case "popUpOverSkin":
            case "overSkin":
               _loc8_ = [0.15,0.05];
               _loc9_ = [16777215,16777215];
               break;
            case "popUpDownSkin":
            case "downSkin":
               _loc6_ = [0.15,0.15];
               _loc8_ = [0.05,0];
               _loc9_ = [13421772,13421772];
               break;
            case "disabledSkin":
               _loc6_ = [0.05,0.05];
               _loc7_ = [0,0];
               _loc8_ = [0.3,0.3];
               _loc9_ = [0,0];
         }
         DrawUtil.drawDoubleRect(_loc3_,[16777215,16777215],_loc6_,0,0,_loc4_,_loc5_,1,1,_loc4_ - 2,_loc5_ - 2);
         DrawUtil.drawDoubleRect(_loc3_,[0,0],[0.6,0.6],1,1,_loc4_ - 2,_loc5_ - 2,2,2,_loc4_ - 4,_loc5_ - 4);
         DrawUtil.drawSingleRect(_loc3_,_loc7_,_loc8_,2,2,_loc4_ - 4,_loc5_ - 4);
         DrawUtil.drawDoubleRect(_loc3_,[16777215,16777215],[0.08,0.03],2,2,_loc4_ - 4,_loc5_ - 4,3,3,_loc4_ - 6,_loc5_ - 6);
         DrawUtil.drawSingleRect(_loc3_,[16777215,16777215],[0.1,0.05],_loc4_ - 18 - 1,2,1,_loc5_ - 4);
         DrawUtil.drawSingleRect(_loc3_,[0,0],[0.85,0.6],_loc4_ - 18,2,1,_loc5_ - 4);
         DrawUtil.drawSingleRect(_loc3_,[16777215,16777215],[0.1,0.05],_loc4_ - 18 + 1,2,1,_loc5_ - 4);
         DrawUtil.drawArrow(_loc3_,5,_loc9_,[1,1],_loc4_ - 13,Math.floor(_loc5_ / 2) - Math.floor(5 / 2));
      }
   }
}

