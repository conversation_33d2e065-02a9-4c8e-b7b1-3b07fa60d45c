package flashx.textLayout.formats
{
   public final class ListStyleType
   {
      
      public static const UPPER_ALPHA:String = "upperAlpha";
      
      public static const LOWER_ALPHA:String = "lowerAlpha";
      
      public static const UPPER_ROMAN:String = "upperRoman";
      
      public static const LOWER_ROMAN:String = "lowerRoman";
      
      public static const NONE:String = "none";
      
      public static const DISC:String = "disc";
      
      public static const CIRCLE:String = "circle";
      
      public static const SQUARE:String = "square";
      
      public static const BOX:String = "box";
      
      public static const CHECK:String = "check";
      
      public static const DIAMOND:String = "diamond";
      
      public static const HYPHEN:String = "hyphen";
      
      public static const ARABIC_INDIC:String = "arabicIndic";
      
      public static const BENGALI:String = "bengali";
      
      public static const DECIMAL:String = "decimal";
      
      public static const DECIMAL_LEADING_ZERO:String = "decimalLeadingZero";
      
      public static const DEVANAGARI:String = "devanagari";
      
      public static const GUJARATI:String = "gujarati";
      
      public static const GURMUKHI:String = "gurmukhi";
      
      public static const KANNADA:String = "kannada";
      
      public static const PERSIAN:String = "persian";
      
      public static const THAI:String = "thai";
      
      public static const URDU:String = "urdu";
      
      public static const CJK_EARTHLY_BRANCH:String = "cjkEarthlyBranch";
      
      public static const CJK_HEAVENLY_STEM:String = "cjkHeavenlyStem";
      
      public static const HANGUL:String = "hangul";
      
      public static const HANGUL_CONSTANT:String = "hangulConstant";
      
      public static const HIRAGANA:String = "hiragana";
      
      public static const HIRAGANA_IROHA:String = "hiraganaIroha";
      
      public static const KATAKANA:String = "katakana";
      
      public static const KATAKANA_IROHA:String = "katakanaIroha";
      
      public static const LOWER_GREEK:String = "lowerGreek";
      
      public static const LOWER_LATIN:String = "lowerLatin";
      
      public static const UPPER_GREEK:String = "upperGreek";
      
      public static const UPPER_LATIN:String = "upperLatin";
      
      public function ListStyleType()
      {
         super();
      }
   }
}

