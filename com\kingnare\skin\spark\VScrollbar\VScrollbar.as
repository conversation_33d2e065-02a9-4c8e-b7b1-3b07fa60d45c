package com.kingnare.skin.spark.VScrollbar
{
   import mx.binding.BindingManager;
   import mx.core.IFlexModuleFactory;
   import mx.core.IStateClient2;
   import mx.events.PropertyChangeEvent;
   import mx.graphics.SolidColor;
   import mx.states.SetProperty;
   import mx.states.State;
   import spark.components.Button;
   import spark.components.VScrollBar;
   import spark.components.supportClasses.Skin;
   import spark.primitives.Rect;
   
   public class VScrollbar extends Skin implements IStateClient2
   {
      
      private var _853009829decrementButton:Button;
      
      private var _454226047incrementButton:Button;
      
      private var _110342614thumb:Button;
      
      private var _110621003track:Button;
      
      private var __moduleFactoryInitialized:Boolean = false;
      
      private var _213507019hostComponent:VScrollBar;
      
      public function VScrollbar()
      {
         super();
         mx_internal::_document = this;
         this.width = 13;
         this.height = 200;
         this.mxmlContent = [this._VScrollbar_Rect1_c(),this._VScrollbar_Button1_i(),this._VScrollbar_Button2_i(),this._VScrollbar_Button3_i(),this._VScrollbar_Button4_i()];
         this.currentState = "normal";
         states = [new State({
            "name":"normal",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"decrementButton",
               "name":"top",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"decrementButton",
               "name":"height",
               "value":18
            }),new SetProperty().initializeFromObject({
               "target":"incrementButton",
               "name":"x",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"incrementButton",
               "name":"bottom",
               "value":0
            })]
         }),new State({
            "name":"disabled",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"track",
               "name":"alpha",
               "value":0.5
            }),new SetProperty().initializeFromObject({
               "target":"thumb",
               "name":"alpha",
               "value":0.5
            }),new SetProperty().initializeFromObject({
               "target":"decrementButton",
               "name":"alpha",
               "value":0.5
            }),new SetProperty().initializeFromObject({
               "target":"incrementButton",
               "name":"bottom",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"incrementButton",
               "name":"alpha",
               "value":0.5
            })]
         }),new State({
            "name":"inactive",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"track",
               "name":"alpha",
               "value":0.5
            }),new SetProperty().initializeFromObject({
               "target":"thumb",
               "name":"visible",
               "value":false
            }),new SetProperty().initializeFromObject({
               "target":"thumb",
               "name":"alpha",
               "value":0.5
            }),new SetProperty().initializeFromObject({
               "target":"decrementButton",
               "name":"alpha",
               "value":0.5
            }),new SetProperty().initializeFromObject({
               "target":"decrementButton",
               "name":"enabled",
               "value":false
            }),new SetProperty().initializeFromObject({
               "target":"incrementButton",
               "name":"bottom",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"incrementButton",
               "name":"alpha",
               "value":0.5
            }),new SetProperty().initializeFromObject({
               "target":"incrementButton",
               "name":"enabled",
               "value":false
            })]
         })];
      }
      
      override public function set moduleFactory(param1:IFlexModuleFactory) : void
      {
         super.moduleFactory = param1;
         if(this.__moduleFactoryInitialized)
         {
            return;
         }
         this.__moduleFactoryInitialized = true;
      }
      
      override public function initialize() : void
      {
         super.initialize();
      }
      
      private function _VScrollbar_Rect1_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.percentWidth = 100;
         _loc1_.percentHeight = 100;
         _loc1_.fill = this._VScrollbar_SolidColor1_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _VScrollbar_SolidColor1_c() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 3355443;
         return _loc1_;
      }
      
      private function _VScrollbar_Button1_i() : Button
      {
         var _loc1_:Button = new Button();
         _loc1_.x = 0;
         _loc1_.top = 17;
         _loc1_.bottom = 17;
         _loc1_.focusEnabled = false;
         _loc1_.setStyle("skinClass",VScrollbarTrack);
         _loc1_.id = "track";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         this.track = _loc1_;
         BindingManager.executeBindings(this,"track",this.track);
         return _loc1_;
      }
      
      private function _VScrollbar_Button2_i() : Button
      {
         var _loc1_:Button = new Button();
         _loc1_.x = 0;
         _loc1_.y = 17;
         _loc1_.focusEnabled = false;
         _loc1_.setStyle("skinClass",VScrollbarThumb);
         _loc1_.id = "thumb";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         this.thumb = _loc1_;
         BindingManager.executeBindings(this,"thumb",this.thumb);
         return _loc1_;
      }
      
      private function _VScrollbar_Button3_i() : Button
      {
         var _loc1_:Button = new Button();
         _loc1_.x = 0;
         _loc1_.top = 0;
         _loc1_.focusEnabled = false;
         _loc1_.setStyle("skinClass",VScrollbarUpButton);
         _loc1_.id = "decrementButton";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         this.decrementButton = _loc1_;
         BindingManager.executeBindings(this,"decrementButton",this.decrementButton);
         return _loc1_;
      }
      
      private function _VScrollbar_Button4_i() : Button
      {
         var _loc1_:Button = new Button();
         _loc1_.x = 0;
         _loc1_.focusEnabled = false;
         _loc1_.setStyle("skinClass",VScrollbarDownButton);
         _loc1_.id = "incrementButton";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         this.incrementButton = _loc1_;
         BindingManager.executeBindings(this,"incrementButton",this.incrementButton);
         return _loc1_;
      }
      
      [Bindable(event="propertyChange")]
      public function get decrementButton() : Button
      {
         return this._853009829decrementButton;
      }
      
      public function set decrementButton(param1:Button) : void
      {
         var _loc2_:Object = this._853009829decrementButton;
         if(_loc2_ !== param1)
         {
            this._853009829decrementButton = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"decrementButton",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get incrementButton() : Button
      {
         return this._454226047incrementButton;
      }
      
      public function set incrementButton(param1:Button) : void
      {
         var _loc2_:Object = this._454226047incrementButton;
         if(_loc2_ !== param1)
         {
            this._454226047incrementButton = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"incrementButton",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get thumb() : Button
      {
         return this._110342614thumb;
      }
      
      public function set thumb(param1:Button) : void
      {
         var _loc2_:Object = this._110342614thumb;
         if(_loc2_ !== param1)
         {
            this._110342614thumb = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"thumb",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get track() : Button
      {
         return this._110621003track;
      }
      
      public function set track(param1:Button) : void
      {
         var _loc2_:Object = this._110621003track;
         if(_loc2_ !== param1)
         {
            this._110621003track = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"track",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get hostComponent() : VScrollBar
      {
         return this._213507019hostComponent;
      }
      
      public function set hostComponent(param1:VScrollBar) : void
      {
         var _loc2_:Object = this._213507019hostComponent;
         if(_loc2_ !== param1)
         {
            this._213507019hostComponent = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"hostComponent",_loc2_,param1));
            }
         }
      }
   }
}

