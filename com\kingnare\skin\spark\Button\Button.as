package com.kingnare.skin.spark.Button
{
   import mx.binding.BindingManager;
   import mx.core.IFlexModuleFactory;
   import mx.core.IStateClient2;
   import mx.events.PropertyChangeEvent;
   import mx.graphics.GradientEntry;
   import mx.graphics.LinearGradient;
   import mx.graphics.LinearGradientStroke;
   import mx.graphics.SolidColorStroke;
   import mx.states.SetProperty;
   import mx.states.State;
   import spark.components.Button;
   import spark.components.Label;
   import spark.layouts.BasicLayout;
   import spark.primitives.Rect;
   import spark.skins.SparkSkin;
   
   public class But<PERSON> extends SparkSkin implements IStateClient2
   {
      
      private var _1087233789_Button_GradientEntry1:GradientEntry;
      
      private var _1087233790_Button_GradientEntry2:GradientEntry;
      
      private var _1087233791_Button_GradientEntry3:GradientEntry;
      
      private var _1087233792_Button_GradientEntry4:GradientEntry;
      
      private var _656139763_Button_SolidColorStroke1:SolidColorStroke;
      
      private var _1780999580innerLightRect:Rect;
      
      private var _2100685128innerLightStroke:Rect;
      
      private var _528023206innerRect:Rect;
      
      private var _1184053038labelDisplay:Label;
      
      private var _685606938lightRect:Rect;
      
      private var __moduleFactoryInitialized:Boolean = false;
      
      private var _213507019hostComponent:spark.components.Button;
      
      public function Button()
      {
         super();
         mx_internal::_document = this;
         this.layout = this._Button_BasicLayout1_c();
         this.mxmlContent = [this._Button_Rect1_i(),this._Button_Rect2_i(),this._Button_Rect3_i(),this._Button_Rect4_i(),this._Button_Label1_i()];
         this.currentState = "up";
         states = [new State({
            "name":"up",
            "overrides":[]
         }),new State({
            "name":"down",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_Button_SolidColorStroke1",
               "name":"alpha",
               "value":0.15
            }),new SetProperty().initializeFromObject({
               "target":"_Button_GradientEntry1",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_Button_GradientEntry1",
               "name":"alpha",
               "value":0.05
            }),new SetProperty().initializeFromObject({
               "target":"_Button_GradientEntry2",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_Button_GradientEntry2",
               "name":"alpha",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_Button_GradientEntry3",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_Button_GradientEntry3",
               "name":"alpha",
               "value":0.2
            }),new SetProperty().initializeFromObject({
               "target":"_Button_GradientEntry4",
               "name":"ratio",
               "value":1
            }),new SetProperty().initializeFromObject({
               "target":"_Button_GradientEntry4",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_Button_GradientEntry4",
               "name":"alpha",
               "value":0.1
            })]
         }),new State({
            "name":"over",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_Button_GradientEntry1",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_Button_GradientEntry1",
               "name":"alpha",
               "value":0.15
            }),new SetProperty().initializeFromObject({
               "target":"_Button_GradientEntry2",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_Button_GradientEntry2",
               "name":"alpha",
               "value":0.05
            })]
         }),new State({
            "name":"disabled",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_Button_SolidColorStroke1",
               "name":"alpha",
               "value":0.05
            }),new SetProperty().initializeFromObject({
               "target":"_Button_GradientEntry1",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_Button_GradientEntry1",
               "name":"alpha",
               "value":0.3
            }),new SetProperty().initializeFromObject({
               "target":"_Button_GradientEntry2",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_Button_GradientEntry2",
               "name":"alpha",
               "value":0.3
            }),new SetProperty().initializeFromObject({
               "target":"_Button_GradientEntry3",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_Button_GradientEntry3",
               "name":"alpha",
               "value":0.05
            }),new SetProperty().initializeFromObject({
               "target":"_Button_GradientEntry4",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_Button_GradientEntry4",
               "name":"alpha",
               "value":0.05
            })]
         })];
      }
      
      override public function set moduleFactory(param1:IFlexModuleFactory) : void
      {
         super.moduleFactory = param1;
         if(this.__moduleFactoryInitialized)
         {
            return;
         }
         this.__moduleFactoryInitialized = true;
      }
      
      override public function initialize() : void
      {
         super.initialize();
      }
      
      private function _Button_BasicLayout1_c() : BasicLayout
      {
         return new BasicLayout();
      }
      
      private function _Button_Rect1_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 0;
         _loc1_.right = 0;
         _loc1_.top = 0;
         _loc1_.bottom = 0;
         _loc1_.stroke = this._Button_SolidColorStroke1_i();
         _loc1_.initialized(this,"lightRect");
         this.lightRect = _loc1_;
         BindingManager.executeBindings(this,"lightRect",this.lightRect);
         return _loc1_;
      }
      
      private function _Button_SolidColorStroke1_i() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.color = 16777215;
         _loc1_.caps = "none";
         _loc1_.joints = "miter";
         _loc1_.miterLimit = 4;
         _loc1_.weight = 1;
         _loc1_.alpha = 0.1;
         this._Button_SolidColorStroke1 = _loc1_;
         BindingManager.executeBindings(this,"_Button_SolidColorStroke1",this._Button_SolidColorStroke1);
         return _loc1_;
      }
      
      private function _Button_Rect2_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.top = 1;
         _loc1_.bottom = 1;
         _loc1_.right = 1;
         _loc1_.left = 1;
         _loc1_.stroke = this._Button_SolidColorStroke2_c();
         _loc1_.initialized(this,"innerRect");
         this.innerRect = _loc1_;
         BindingManager.executeBindings(this,"innerRect",this.innerRect);
         return _loc1_;
      }
      
      private function _Button_SolidColorStroke2_c() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.weight = 1;
         _loc1_.caps = "none";
         _loc1_.miterLimit = 4;
         _loc1_.joints = "miter";
         _loc1_.color = 0;
         _loc1_.alpha = 0.6;
         return _loc1_;
      }
      
      private function _Button_Rect3_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.top = 2;
         _loc1_.right = 2;
         _loc1_.left = 2;
         _loc1_.bottom = 2;
         _loc1_.fill = this._Button_LinearGradient1_c();
         _loc1_.initialized(this,"innerLightRect");
         this.innerLightRect = _loc1_;
         BindingManager.executeBindings(this,"innerLightRect",this.innerLightRect);
         return _loc1_;
      }
      
      private function _Button_LinearGradient1_c() : LinearGradient
      {
         var _loc1_:LinearGradient = new LinearGradient();
         _loc1_.rotation = 90;
         _loc1_.entries = [this._Button_GradientEntry1_i(),this._Button_GradientEntry2_i()];
         return _loc1_;
      }
      
      private function _Button_GradientEntry1_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 16777215;
         _loc1_.alpha = 0.1;
         _loc1_.ratio = 0;
         this._Button_GradientEntry1 = _loc1_;
         BindingManager.executeBindings(this,"_Button_GradientEntry1",this._Button_GradientEntry1);
         return _loc1_;
      }
      
      private function _Button_GradientEntry2_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 16777215;
         _loc1_.alpha = 0;
         _loc1_.ratio = 1;
         this._Button_GradientEntry2 = _loc1_;
         BindingManager.executeBindings(this,"_Button_GradientEntry2",this._Button_GradientEntry2);
         return _loc1_;
      }
      
      private function _Button_Rect4_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.top = 2;
         _loc1_.right = 2;
         _loc1_.left = 2;
         _loc1_.bottom = 2;
         _loc1_.stroke = this._Button_LinearGradientStroke1_c();
         _loc1_.initialized(this,"innerLightStroke");
         this.innerLightStroke = _loc1_;
         BindingManager.executeBindings(this,"innerLightStroke",this.innerLightStroke);
         return _loc1_;
      }
      
      private function _Button_LinearGradientStroke1_c() : LinearGradientStroke
      {
         var _loc1_:LinearGradientStroke = new LinearGradientStroke();
         _loc1_.weight = 1;
         _loc1_.caps = "none";
         _loc1_.miterLimit = 4;
         _loc1_.joints = "miter";
         _loc1_.rotation = 90;
         _loc1_.entries = [this._Button_GradientEntry3_i(),this._Button_GradientEntry4_i()];
         return _loc1_;
      }
      
      private function _Button_GradientEntry3_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 16777215;
         _loc1_.alpha = 0.08;
         _loc1_.ratio = 0;
         this._Button_GradientEntry3 = _loc1_;
         BindingManager.executeBindings(this,"_Button_GradientEntry3",this._Button_GradientEntry3);
         return _loc1_;
      }
      
      private function _Button_GradientEntry4_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 16777215;
         _loc1_.alpha = 0.03;
         _loc1_.ratio = 1;
         this._Button_GradientEntry4 = _loc1_;
         BindingManager.executeBindings(this,"_Button_GradientEntry4",this._Button_GradientEntry4);
         return _loc1_;
      }
      
      private function _Button_Label1_i() : Label
      {
         var _loc1_:Label = new Label();
         _loc1_.text = "(Label)";
         _loc1_.left = 10;
         _loc1_.right = 10;
         _loc1_.top = 5;
         _loc1_.bottom = 5;
         _loc1_.horizontalCenter = 0;
         _loc1_.verticalCenter = 1;
         _loc1_.setStyle("textAlign","center");
         _loc1_.setStyle("verticalAlign","middle");
         _loc1_.id = "labelDisplay";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         this.labelDisplay = _loc1_;
         BindingManager.executeBindings(this,"labelDisplay",this.labelDisplay);
         return _loc1_;
      }
      
      [Bindable(event="propertyChange")]
      public function get _Button_GradientEntry1() : GradientEntry
      {
         return this._1087233789_Button_GradientEntry1;
      }
      
      public function set _Button_GradientEntry1(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._1087233789_Button_GradientEntry1;
         if(_loc2_ !== param1)
         {
            this._1087233789_Button_GradientEntry1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_Button_GradientEntry1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _Button_GradientEntry2() : GradientEntry
      {
         return this._1087233790_Button_GradientEntry2;
      }
      
      public function set _Button_GradientEntry2(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._1087233790_Button_GradientEntry2;
         if(_loc2_ !== param1)
         {
            this._1087233790_Button_GradientEntry2 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_Button_GradientEntry2",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _Button_GradientEntry3() : GradientEntry
      {
         return this._1087233791_Button_GradientEntry3;
      }
      
      public function set _Button_GradientEntry3(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._1087233791_Button_GradientEntry3;
         if(_loc2_ !== param1)
         {
            this._1087233791_Button_GradientEntry3 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_Button_GradientEntry3",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _Button_GradientEntry4() : GradientEntry
      {
         return this._1087233792_Button_GradientEntry4;
      }
      
      public function set _Button_GradientEntry4(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._1087233792_Button_GradientEntry4;
         if(_loc2_ !== param1)
         {
            this._1087233792_Button_GradientEntry4 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_Button_GradientEntry4",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _Button_SolidColorStroke1() : SolidColorStroke
      {
         return this._656139763_Button_SolidColorStroke1;
      }
      
      public function set _Button_SolidColorStroke1(param1:SolidColorStroke) : void
      {
         var _loc2_:Object = this._656139763_Button_SolidColorStroke1;
         if(_loc2_ !== param1)
         {
            this._656139763_Button_SolidColorStroke1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_Button_SolidColorStroke1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get innerLightRect() : Rect
      {
         return this._1780999580innerLightRect;
      }
      
      public function set innerLightRect(param1:Rect) : void
      {
         var _loc2_:Object = this._1780999580innerLightRect;
         if(_loc2_ !== param1)
         {
            this._1780999580innerLightRect = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"innerLightRect",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get innerLightStroke() : Rect
      {
         return this._2100685128innerLightStroke;
      }
      
      public function set innerLightStroke(param1:Rect) : void
      {
         var _loc2_:Object = this._2100685128innerLightStroke;
         if(_loc2_ !== param1)
         {
            this._2100685128innerLightStroke = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"innerLightStroke",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get innerRect() : Rect
      {
         return this._528023206innerRect;
      }
      
      public function set innerRect(param1:Rect) : void
      {
         var _loc2_:Object = this._528023206innerRect;
         if(_loc2_ !== param1)
         {
            this._528023206innerRect = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"innerRect",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get labelDisplay() : Label
      {
         return this._1184053038labelDisplay;
      }
      
      public function set labelDisplay(param1:Label) : void
      {
         var _loc2_:Object = this._1184053038labelDisplay;
         if(_loc2_ !== param1)
         {
            this._1184053038labelDisplay = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"labelDisplay",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get lightRect() : Rect
      {
         return this._685606938lightRect;
      }
      
      public function set lightRect(param1:Rect) : void
      {
         var _loc2_:Object = this._685606938lightRect;
         if(_loc2_ !== param1)
         {
            this._685606938lightRect = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"lightRect",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get hostComponent() : spark.components.Button
      {
         return this._213507019hostComponent;
      }
      
      public function set hostComponent(param1:spark.components.Button) : void
      {
         var _loc2_:Object = this._213507019hostComponent;
         if(_loc2_ !== param1)
         {
            this._213507019hostComponent = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"hostComponent",_loc2_,param1));
            }
         }
      }
   }
}

