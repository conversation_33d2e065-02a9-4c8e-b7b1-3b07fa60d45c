package com.kingnare.skin.spark.VScrollbar
{
   import mx.binding.BindingManager;
   import mx.core.IFlexModuleFactory;
   import mx.core.IStateClient2;
   import mx.events.PropertyChangeEvent;
   import mx.graphics.SolidColor;
   import mx.graphics.SolidColorStroke;
   import mx.states.SetProperty;
   import mx.states.State;
   import spark.components.Button;
   import spark.components.Group;
   import spark.components.supportClasses.Skin;
   import spark.primitives.Line;
   import spark.primitives.Rect;
   
   public class VScrollbarThumb extends Skin implements IStateClient2
   {
      
      private var _1164158442_VScrollbarThumb_Group1:Group;
      
      private var _333083039_VScrollbarThumb_SolidColor1:SolidColor;
      
      private var _260051721_VScrollbarThumb_SolidColorStroke1:SolidColorStroke;
      
      private var _260051722_VScrollbarThumb_SolidColorStroke2:SolidColorStroke;
      
      private var _260051724_VScrollbarThumb_SolidColorStroke4:SolidColorStroke;
      
      private var _260051726_VScrollbarThumb_SolidColorStroke6:SolidColorStroke;
      
      private var _260051728_VScrollbarThumb_SolidColorStroke8:SolidColorStroke;
      
      private var __moduleFactoryInitialized:Boolean = false;
      
      private var _213507019hostComponent:Button;
      
      public function VScrollbarThumb()
      {
         super();
         mx_internal::_document = this;
         this.width = 13;
         this.mxmlContent = [this._VScrollbarThumb_Rect1_c(),this._VScrollbarThumb_Rect2_c(),this._VScrollbarThumb_Rect3_c(),this._VScrollbarThumb_Group1_i()];
         this.currentState = "up";
         states = [new State({
            "name":"up",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_VScrollbarThumb_Group1",
               "name":"transformY",
               "value":-1
            }),new SetProperty().initializeFromObject({
               "target":"_VScrollbarThumb_SolidColorStroke4",
               "name":"alpha",
               "value":0.5
            }),new SetProperty().initializeFromObject({
               "target":"_VScrollbarThumb_SolidColorStroke6",
               "name":"alpha",
               "value":0.5
            }),new SetProperty().initializeFromObject({
               "target":"_VScrollbarThumb_SolidColorStroke8",
               "name":"alpha",
               "value":0.5
            })]
         }),new State({
            "name":"over",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_VScrollbarThumb_SolidColorStroke2",
               "name":"alpha",
               "value":0.23
            }),new SetProperty().initializeFromObject({
               "target":"_VScrollbarThumb_SolidColor1",
               "name":"alpha",
               "value":0.1
            }),new SetProperty().initializeFromObject({
               "target":"_VScrollbarThumb_Group1",
               "name":"transformY",
               "value":-1
            })]
         }),new State({
            "name":"down",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_VScrollbarThumb_Group1",
               "name":"transformY",
               "value":-1
            }),new SetProperty().initializeFromObject({
               "target":"_VScrollbarThumb_SolidColorStroke4",
               "name":"alpha",
               "value":0.5
            }),new SetProperty().initializeFromObject({
               "target":"_VScrollbarThumb_SolidColorStroke6",
               "name":"alpha",
               "value":0.5
            }),new SetProperty().initializeFromObject({
               "target":"_VScrollbarThumb_SolidColorStroke8",
               "name":"alpha",
               "value":0.5
            })]
         }),new State({
            "name":"disabled",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_VScrollbarThumb_SolidColorStroke1",
               "name":"color",
               "value":3355443
            }),new SetProperty().initializeFromObject({
               "target":"_VScrollbarThumb_Group1",
               "name":"transformY",
               "value":-1
            }),new SetProperty().initializeFromObject({
               "target":"_VScrollbarThumb_SolidColorStroke4",
               "name":"alpha",
               "value":0.5
            }),new SetProperty().initializeFromObject({
               "target":"_VScrollbarThumb_SolidColorStroke6",
               "name":"alpha",
               "value":0.5
            }),new SetProperty().initializeFromObject({
               "target":"_VScrollbarThumb_SolidColorStroke8",
               "name":"alpha",
               "value":0.5
            })]
         })];
      }
      
      override public function set moduleFactory(param1:IFlexModuleFactory) : void
      {
         super.moduleFactory = param1;
         if(this.__moduleFactoryInitialized)
         {
            return;
         }
         this.__moduleFactoryInitialized = true;
      }
      
      override public function initialize() : void
      {
         super.initialize();
      }
      
      private function _VScrollbarThumb_Rect1_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.x = 0.5;
         _loc1_.width = 12;
         _loc1_.top = 0;
         _loc1_.bottom = 0;
         _loc1_.stroke = this._VScrollbarThumb_SolidColorStroke1_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _VScrollbarThumb_SolidColorStroke1_i() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.alpha = 0.6;
         _loc1_.caps = "square";
         _loc1_.color = 0;
         _loc1_.joints = "miter";
         this._VScrollbarThumb_SolidColorStroke1 = _loc1_;
         BindingManager.executeBindings(this,"_VScrollbarThumb_SolidColorStroke1",this._VScrollbarThumb_SolidColorStroke1);
         return _loc1_;
      }
      
      private function _VScrollbarThumb_Rect2_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.x = 1.5;
         _loc1_.width = 10;
         _loc1_.height = 27;
         _loc1_.top = 1;
         _loc1_.bottom = 1;
         _loc1_.stroke = this._VScrollbarThumb_SolidColorStroke2_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _VScrollbarThumb_SolidColorStroke2_i() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.alpha = 0.15;
         _loc1_.caps = "square";
         _loc1_.color = 16777215;
         _loc1_.joints = "miter";
         this._VScrollbarThumb_SolidColorStroke2 = _loc1_;
         BindingManager.executeBindings(this,"_VScrollbarThumb_SolidColorStroke2",this._VScrollbarThumb_SolidColorStroke2);
         return _loc1_;
      }
      
      private function _VScrollbarThumb_Rect3_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.x = 2;
         _loc1_.width = 9;
         _loc1_.height = 26;
         _loc1_.top = 2;
         _loc1_.bottom = 2;
         _loc1_.fill = this._VScrollbarThumb_SolidColor1_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _VScrollbarThumb_SolidColor1_i() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.alpha = 0.05;
         _loc1_.color = 16777215;
         this._VScrollbarThumb_SolidColor1 = _loc1_;
         BindingManager.executeBindings(this,"_VScrollbarThumb_SolidColor1",this._VScrollbarThumb_SolidColor1);
         return _loc1_;
      }
      
      private function _VScrollbarThumb_Group1_i() : Group
      {
         var _loc1_:Group = new Group();
         _loc1_.horizontalCenter = 0;
         _loc1_.verticalCenter = 0;
         _loc1_.mxmlContent = [this._VScrollbarThumb_Line1_c(),this._VScrollbarThumb_Line2_c(),this._VScrollbarThumb_Line3_c(),this._VScrollbarThumb_Line4_c(),this._VScrollbarThumb_Line5_c(),this._VScrollbarThumb_Line6_c()];
         _loc1_.id = "_VScrollbarThumb_Group1";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         this._VScrollbarThumb_Group1 = _loc1_;
         BindingManager.executeBindings(this,"_VScrollbarThumb_Group1",this._VScrollbarThumb_Group1);
         return _loc1_;
      }
      
      private function _VScrollbarThumb_Line1_c() : Line
      {
         var _loc1_:Line = new Line();
         _loc1_.x = 0.5;
         _loc1_.y = 2.5;
         _loc1_.xFrom = 0;
         _loc1_.xTo = 2;
         _loc1_.yFrom = 1;
         _loc1_.yTo = 1;
         _loc1_.stroke = this._VScrollbarThumb_SolidColorStroke3_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _VScrollbarThumb_SolidColorStroke3_c() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.alpha = 0.4;
         _loc1_.caps = "square";
         _loc1_.color = 0;
         _loc1_.joints = "miter";
         _loc1_.pixelHinting = true;
         return _loc1_;
      }
      
      private function _VScrollbarThumb_Line2_c() : Line
      {
         var _loc1_:Line = new Line();
         _loc1_.x = 0.5;
         _loc1_.y = 1.5;
         _loc1_.xFrom = 0;
         _loc1_.xTo = 2;
         _loc1_.yFrom = 1;
         _loc1_.yTo = 1;
         _loc1_.stroke = this._VScrollbarThumb_SolidColorStroke4_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _VScrollbarThumb_SolidColorStroke4_i() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.caps = "square";
         _loc1_.color = 16777215;
         _loc1_.joints = "miter";
         _loc1_.pixelHinting = true;
         this._VScrollbarThumb_SolidColorStroke4 = _loc1_;
         BindingManager.executeBindings(this,"_VScrollbarThumb_SolidColorStroke4",this._VScrollbarThumb_SolidColorStroke4);
         return _loc1_;
      }
      
      private function _VScrollbarThumb_Line3_c() : Line
      {
         var _loc1_:Line = new Line();
         _loc1_.x = 0.5;
         _loc1_.y = 4.5;
         _loc1_.xFrom = 0;
         _loc1_.xTo = 2;
         _loc1_.yFrom = 1;
         _loc1_.yTo = 1;
         _loc1_.stroke = this._VScrollbarThumb_SolidColorStroke5_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _VScrollbarThumb_SolidColorStroke5_c() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.alpha = 0.4;
         _loc1_.caps = "square";
         _loc1_.color = 0;
         _loc1_.joints = "miter";
         _loc1_.pixelHinting = true;
         return _loc1_;
      }
      
      private function _VScrollbarThumb_Line4_c() : Line
      {
         var _loc1_:Line = new Line();
         _loc1_.x = 0.5;
         _loc1_.y = 3.5;
         _loc1_.xFrom = 0;
         _loc1_.xTo = 2;
         _loc1_.yFrom = 1;
         _loc1_.yTo = 1;
         _loc1_.stroke = this._VScrollbarThumb_SolidColorStroke6_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _VScrollbarThumb_SolidColorStroke6_i() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.caps = "square";
         _loc1_.color = 16777215;
         _loc1_.joints = "miter";
         _loc1_.pixelHinting = true;
         this._VScrollbarThumb_SolidColorStroke6 = _loc1_;
         BindingManager.executeBindings(this,"_VScrollbarThumb_SolidColorStroke6",this._VScrollbarThumb_SolidColorStroke6);
         return _loc1_;
      }
      
      private function _VScrollbarThumb_Line5_c() : Line
      {
         var _loc1_:Line = new Line();
         _loc1_.x = 0.5;
         _loc1_.y = 0.5;
         _loc1_.xFrom = 0;
         _loc1_.xTo = 2;
         _loc1_.yFrom = 1;
         _loc1_.yTo = 1;
         _loc1_.stroke = this._VScrollbarThumb_SolidColorStroke7_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _VScrollbarThumb_SolidColorStroke7_c() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.alpha = 0.4;
         _loc1_.caps = "square";
         _loc1_.color = 0;
         _loc1_.joints = "miter";
         _loc1_.pixelHinting = true;
         return _loc1_;
      }
      
      private function _VScrollbarThumb_Line6_c() : Line
      {
         var _loc1_:Line = new Line();
         _loc1_.x = 0.5;
         _loc1_.y = -0.5;
         _loc1_.xFrom = 0;
         _loc1_.xTo = 2;
         _loc1_.yFrom = 1;
         _loc1_.yTo = 1;
         _loc1_.stroke = this._VScrollbarThumb_SolidColorStroke8_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _VScrollbarThumb_SolidColorStroke8_i() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.caps = "square";
         _loc1_.color = 16777215;
         _loc1_.joints = "miter";
         _loc1_.pixelHinting = true;
         this._VScrollbarThumb_SolidColorStroke8 = _loc1_;
         BindingManager.executeBindings(this,"_VScrollbarThumb_SolidColorStroke8",this._VScrollbarThumb_SolidColorStroke8);
         return _loc1_;
      }
      
      [Bindable(event="propertyChange")]
      public function get _VScrollbarThumb_Group1() : Group
      {
         return this._1164158442_VScrollbarThumb_Group1;
      }
      
      public function set _VScrollbarThumb_Group1(param1:Group) : void
      {
         var _loc2_:Object = this._1164158442_VScrollbarThumb_Group1;
         if(_loc2_ !== param1)
         {
            this._1164158442_VScrollbarThumb_Group1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_VScrollbarThumb_Group1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _VScrollbarThumb_SolidColor1() : SolidColor
      {
         return this._333083039_VScrollbarThumb_SolidColor1;
      }
      
      public function set _VScrollbarThumb_SolidColor1(param1:SolidColor) : void
      {
         var _loc2_:Object = this._333083039_VScrollbarThumb_SolidColor1;
         if(_loc2_ !== param1)
         {
            this._333083039_VScrollbarThumb_SolidColor1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_VScrollbarThumb_SolidColor1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _VScrollbarThumb_SolidColorStroke1() : SolidColorStroke
      {
         return this._260051721_VScrollbarThumb_SolidColorStroke1;
      }
      
      public function set _VScrollbarThumb_SolidColorStroke1(param1:SolidColorStroke) : void
      {
         var _loc2_:Object = this._260051721_VScrollbarThumb_SolidColorStroke1;
         if(_loc2_ !== param1)
         {
            this._260051721_VScrollbarThumb_SolidColorStroke1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_VScrollbarThumb_SolidColorStroke1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _VScrollbarThumb_SolidColorStroke2() : SolidColorStroke
      {
         return this._260051722_VScrollbarThumb_SolidColorStroke2;
      }
      
      public function set _VScrollbarThumb_SolidColorStroke2(param1:SolidColorStroke) : void
      {
         var _loc2_:Object = this._260051722_VScrollbarThumb_SolidColorStroke2;
         if(_loc2_ !== param1)
         {
            this._260051722_VScrollbarThumb_SolidColorStroke2 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_VScrollbarThumb_SolidColorStroke2",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _VScrollbarThumb_SolidColorStroke4() : SolidColorStroke
      {
         return this._260051724_VScrollbarThumb_SolidColorStroke4;
      }
      
      public function set _VScrollbarThumb_SolidColorStroke4(param1:SolidColorStroke) : void
      {
         var _loc2_:Object = this._260051724_VScrollbarThumb_SolidColorStroke4;
         if(_loc2_ !== param1)
         {
            this._260051724_VScrollbarThumb_SolidColorStroke4 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_VScrollbarThumb_SolidColorStroke4",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _VScrollbarThumb_SolidColorStroke6() : SolidColorStroke
      {
         return this._260051726_VScrollbarThumb_SolidColorStroke6;
      }
      
      public function set _VScrollbarThumb_SolidColorStroke6(param1:SolidColorStroke) : void
      {
         var _loc2_:Object = this._260051726_VScrollbarThumb_SolidColorStroke6;
         if(_loc2_ !== param1)
         {
            this._260051726_VScrollbarThumb_SolidColorStroke6 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_VScrollbarThumb_SolidColorStroke6",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _VScrollbarThumb_SolidColorStroke8() : SolidColorStroke
      {
         return this._260051728_VScrollbarThumb_SolidColorStroke8;
      }
      
      public function set _VScrollbarThumb_SolidColorStroke8(param1:SolidColorStroke) : void
      {
         var _loc2_:Object = this._260051728_VScrollbarThumb_SolidColorStroke8;
         if(_loc2_ !== param1)
         {
            this._260051728_VScrollbarThumb_SolidColorStroke8 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_VScrollbarThumb_SolidColorStroke8",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get hostComponent() : Button
      {
         return this._213507019hostComponent;
      }
      
      public function set hostComponent(param1:Button) : void
      {
         var _loc2_:Object = this._213507019hostComponent;
         if(_loc2_ !== param1)
         {
            this._213507019hostComponent = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"hostComponent",_loc2_,param1));
            }
         }
      }
   }
}

