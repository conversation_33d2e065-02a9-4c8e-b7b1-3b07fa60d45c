package mx.accessibility
{
   import mx.core.mx_internal;
   
   use namespace mx_internal;
   
   public final class AccConst
   {
      
      mx_internal static const VERSION:String = "4.6.0.23201";
      
      public static const ROLE_SYSTEM_TITLEBAR:uint = 1;
      
      public static const ROLE_SYSTEM_MENUBAR:uint = 2;
      
      public static const ROLE_SYSTEM_SCROLLBAR:uint = 3;
      
      public static const ROLE_SYSTEM_GRIP:uint = 4;
      
      public static const ROLE_SYSTEM_SOUND:uint = 5;
      
      public static const ROLE_SYSTEM_CURSOR:uint = 6;
      
      public static const ROLE_SYSTEM_CARET:uint = 7;
      
      public static const ROLE_SYSTEM_ALERT:uint = 8;
      
      public static const ROLE_SYSTEM_WINDOW:uint = 9;
      
      public static const ROLE_SYSTEM_CLIENT:uint = 10;
      
      public static const ROLE_SYSTEM_MENUPOPUP:uint = 11;
      
      public static const ROLE_SYSTEM_MENUITEM:uint = 12;
      
      public static const ROLE_SYSTEM_TOOLTIP:uint = 13;
      
      public static const ROLE_SYSTEM_APPLICATION:uint = 14;
      
      public static const ROLE_SYSTEM_DOCUMENT:uint = 15;
      
      public static const ROLE_SYSTEM_PANE:uint = 16;
      
      public static const ROLE_SYSTEM_CHART:uint = 17;
      
      public static const ROLE_SYSTEM_DIALOG:uint = 18;
      
      public static const ROLE_SYSTEM_BORDER:uint = 19;
      
      public static const ROLE_SYSTEM_GROUPING:uint = 20;
      
      public static const ROLE_SYSTEM_SEPARATOR:uint = 21;
      
      public static const ROLE_SYSTEM_TOOLBAR:uint = 22;
      
      public static const ROLE_SYSTEM_STATUSBAR:uint = 23;
      
      public static const ROLE_SYSTEM_TABLE:uint = 24;
      
      public static const ROLE_SYSTEM_COLUMNHEADER:uint = 25;
      
      public static const ROLE_SYSTEM_ROWHEADER:uint = 26;
      
      public static const ROLE_SYSTEM_COLUMN:uint = 27;
      
      public static const ROLE_SYSTEM_ROW:uint = 28;
      
      public static const ROLE_SYSTEM_CELL:uint = 29;
      
      public static const ROLE_SYSTEM_LINK:uint = 30;
      
      public static const ROLE_SYSTEM_HELPBALLOON:uint = 31;
      
      public static const ROLE_SYSTEM_CHARACTER:uint = 32;
      
      public static const ROLE_SYSTEM_LIST:uint = 33;
      
      public static const ROLE_SYSTEM_LISTITEM:uint = 34;
      
      public static const ROLE_SYSTEM_OUTLINE:uint = 35;
      
      public static const ROLE_SYSTEM_OUTLINEITEM:uint = 36;
      
      public static const ROLE_SYSTEM_PAGETAB:uint = 37;
      
      public static const ROLE_SYSTEM_PROPERTYPAGE:uint = 38;
      
      public static const ROLE_SYSTEM_INDICATOR:uint = 39;
      
      public static const ROLE_SYSTEM_GRAPHIC:uint = 40;
      
      public static const ROLE_SYSTEM_STATICTEXT:uint = 41;
      
      public static const ROLE_SYSTEM_TEXT:uint = 42;
      
      public static const ROLE_SYSTEM_PUSHBUTTON:uint = 43;
      
      public static const ROLE_SYSTEM_CHECKBUTTON:uint = 44;
      
      public static const ROLE_SYSTEM_RADIOBUTTON:uint = 45;
      
      public static const ROLE_SYSTEM_COMBOBOX:uint = 46;
      
      public static const ROLE_SYSTEM_DROPLIST:uint = 47;
      
      public static const ROLE_SYSTEM_PROGRESSBAR:uint = 48;
      
      public static const ROLE_SYSTEM_DIAL:uint = 49;
      
      public static const ROLE_SYSTEM_HOTKEYFIELD:uint = 50;
      
      public static const ROLE_SYSTEM_SLIDER:uint = 51;
      
      public static const ROLE_SYSTEM_SPINBUTTON:uint = 52;
      
      public static const ROLE_SYSTEM_DIAGRAM:uint = 53;
      
      public static const ROLE_SYSTEM_ANIMATION:uint = 54;
      
      public static const ROLE_SYSTEM_EQUATION:uint = 55;
      
      public static const ROLE_SYSTEM_BUTTONDROPDOWN:uint = 56;
      
      public static const ROLE_SYSTEM_BUTTONMENU:uint = 57;
      
      public static const ROLE_SYSTEM_BUTTONDROPDOWNGRID:uint = 58;
      
      public static const ROLE_SYSTEM_WHITESPACE:uint = 59;
      
      public static const ROLE_SYSTEM_PAGETABLIST:uint = 60;
      
      public static const ROLE_SYSTEM_CLOCK:uint = 61;
      
      public static const ROLE_SYSTEM_SPLITBUTTON:uint = 62;
      
      public static const ROLE_SYSTEM_IPADDRESS:uint = 63;
      
      public static const ROLE_SYSTEM_OUTLINEBUTTON:uint = 64;
      
      public static const STATE_SYSTEM_NORMAL:uint = 0;
      
      public static const STATE_SYSTEM_UNAVAILABLE:uint = 1;
      
      public static const STATE_SYSTEM_SELECTED:uint = 2;
      
      public static const STATE_SYSTEM_FOCUSED:uint = 4;
      
      public static const STATE_SYSTEM_PRESSED:uint = 8;
      
      public static const STATE_SYSTEM_CHECKED:uint = 16;
      
      public static const STATE_SYSTEM_MIXED:uint = 32;
      
      public static const STATE_SYSTEM_READONLY:uint = 64;
      
      public static const STATE_SYSTEM_HOTTRACKED:uint = 128;
      
      public static const STATE_SYSTEM_DEFAULT:uint = 256;
      
      public static const STATE_SYSTEM_EXPANDED:uint = 512;
      
      public static const STATE_SYSTEM_COLLAPSED:uint = 1024;
      
      public static const STATE_SYSTEM_BUSY:uint = 2048;
      
      public static const STATE_SYSTEM_FLOATING:uint = 4096;
      
      public static const STATE_SYSTEM_MARQUEED:uint = 8192;
      
      public static const STATE_SYSTEM_ANIMATED:uint = 16384;
      
      public static const STATE_SYSTEM_INVISIBLE:uint = 32768;
      
      public static const STATE_SYSTEM_OFFSCREEN:uint = 65536;
      
      public static const STATE_SYSTEM_SIZEABLE:uint = 131072;
      
      public static const STATE_SYSTEM_MOVEABLE:uint = 262144;
      
      public static const STATE_SYSTEM_SELFVOICING:uint = 524288;
      
      public static const STATE_SYSTEM_FOCUSABLE:uint = 1048576;
      
      public static const STATE_SYSTEM_SELECTABLE:uint = 2097152;
      
      public static const STATE_SYSTEM_LINKED:uint = 4194304;
      
      public static const STATE_SYSTEM_TRAVERSED:uint = 8388608;
      
      public static const STATE_SYSTEM_MULTISELECTABLE:uint = 16777216;
      
      public static const STATE_SYSTEM_EXTSELECTABLE:uint = 33554432;
      
      public static const STATE_SYSTEM_ALERT_LOW:uint = 67108864;
      
      public static const STATE_SYSTEM_ALERT_MEDIUM:uint = 134217728;
      
      public static const STATE_SYSTEM_ALERT_HIGH:uint = 268435456;
      
      public static const STATE_SYSTEM_PROTECTED:uint = 536870912;
      
      public static const STATE_SYSTEM_HASPOPUP:uint = 1073741824;
      
      public static const STATE_SYSTEM_VALID:uint = 2147483647;
      
      public static const SELFLAG_NONE:uint = 0;
      
      public static const SELFLAG_TAKEFOCUS:uint = 1;
      
      public static const SELFLAG_TAKESELECTION:uint = 2;
      
      public static const SELFLAG_EXTENDSELECTION:uint = 4;
      
      public static const SELFLAG_ADDSELECTION:uint = 8;
      
      public static const SELFLAG_REMOVESELECTION:uint = 16;
      
      public static const SELFLAG_VALID:uint = 31;
      
      public static const EVENT_SYSTEM_SOUND:uint = 1;
      
      public static const EVENT_SYSTEM_ALERT:uint = 2;
      
      public static const EVENT_SYSTEM_FOREGROUND:uint = 3;
      
      public static const EVENT_SYSTEM_MENUSTART:uint = 4;
      
      public static const EVENT_SYSTEM_MENUEND:uint = 5;
      
      public static const EVENT_SYSTEM_MENUPOPUPSTART:uint = 6;
      
      public static const EVENT_SYSTEM_MENUPOPUPEND:uint = 7;
      
      public static const EVENT_SYSTEM_CAPTURESTART:uint = 8;
      
      public static const EVENT_SYSTEM_CAPTUREEND:uint = 9;
      
      public static const EVENT_SYSTEM_MOVESIZESTART:uint = 10;
      
      public static const EVENT_SYSTEM_MOVESIZEEND:uint = 11;
      
      public static const EVENT_SYSTEM_CONTEXTHELPSTART:uint = 12;
      
      public static const EVENT_SYSTEM_CONTEXTHELPEND:uint = 13;
      
      public static const EVENT_SYSTEM_DRAGDROPSTART:uint = 14;
      
      public static const EVENT_SYSTEM_DRAGDROPEND:uint = 15;
      
      public static const EVENT_SYSTEM_DIALOGSTART:uint = 16;
      
      public static const EVENT_SYSTEM_DIALOGEND:uint = 17;
      
      public static const EVENT_SYSTEM_SCROLLINGSTART:uint = 18;
      
      public static const EVENT_SYSTEM_SCROLLINGEND:uint = 19;
      
      public static const EVENT_SYSTEM_SWITCHSTART:uint = 20;
      
      public static const EVENT_SYSTEM_SWITCHEND:uint = 21;
      
      public static const EVENT_SYSTEM_MINIMIZESTART:uint = 22;
      
      public static const EVENT_SYSTEM_MINIMIZEEND:uint = 23;
      
      public static const EVENT_OBJECT_CREATE:uint = 32768;
      
      public static const EVENT_OBJECT_DESTROY:uint = 32769;
      
      public static const EVENT_OBJECT_SHOW:uint = 32770;
      
      public static const EVENT_OBJECT_HIDE:uint = 32771;
      
      public static const EVENT_OBJECT_REORDER:uint = 32772;
      
      public static const EVENT_OBJECT_FOCUS:uint = 32773;
      
      public static const EVENT_OBJECT_SELECTION:uint = 32774;
      
      public static const EVENT_OBJECT_SELECTIONADD:uint = 32775;
      
      public static const EVENT_OBJECT_SELECTIONREMOVE:uint = 32776;
      
      public static const EVENT_OBJECT_SELECTIONWITHIN:uint = 32777;
      
      public static const EVENT_OBJECT_STATECHANGE:uint = 32778;
      
      public static const EVENT_OBJECT_LOCATIONCHANGE:uint = 32779;
      
      public static const EVENT_OBJECT_NAMECHANGE:uint = 32780;
      
      public static const EVENT_OBJECT_DESCRIPTIONCHANGE:uint = 32781;
      
      public static const EVENT_OBJECT_VALUECHANGE:uint = 32782;
      
      public static const EVENT_OBJECT_PARENTCHANGE:uint = 32783;
      
      public static const EVENT_OBJECT_HELPCHANGE:uint = 32784;
      
      public static const EVENT_OBJECT_DEFACTIONCHANGE:uint = 32785;
      
      public static const EVENT_OBJECT_ACCELERATORCHANGE:uint = 32786;
      
      public static const EVENT_OBJECT_INVOKED:uint = 32787;
      
      public static const EVENT_OBJECT_TEXTSELECTIONCHANGED:uint = 32788;
      
      public static const EVENT_OBJECT_CONTENTSCROLLED:uint = 32789;
      
      public function AccConst()
      {
         super();
      }
   }
}

