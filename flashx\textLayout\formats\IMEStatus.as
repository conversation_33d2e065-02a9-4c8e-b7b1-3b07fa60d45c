package flashx.textLayout.formats
{
   public final class IMEStatus
   {
      
      public static const IME_CLAUSE:String = "imeClause";
      
      public static const IME_STATUS:String = "imeStatus";
      
      public static const SELECTED_RAW:String = "selectedRaw";
      
      public static const SELECTED_CONVERTED:String = "selectedConverted";
      
      public static const NOT_SELECTED_RAW:String = "notSelectedRaw";
      
      public static const NOT_SELECTED_CONVERTED:String = "notSelectedConverted";
      
      public static const DEAD_KEY_INPUT_STATE:String = "deadKeyInputState";
      
      public function IMEStatus()
      {
         super();
      }
   }
}

