package com.kingnare.skin.spark.HScrollbar
{
   import mx.binding.BindingManager;
   import mx.core.IFlexModuleFactory;
   import mx.core.IStateClient2;
   import mx.events.PropertyChangeEvent;
   import mx.graphics.SolidColor;
   import mx.graphics.SolidColorStroke;
   import mx.states.SetProperty;
   import mx.states.State;
   import spark.components.Button;
   import spark.components.Group;
   import spark.components.supportClasses.Skin;
   import spark.filters.DropShadowFilter;
   import spark.primitives.Rect;
   
   public class HScrollbarLeftButton extends Skin implements IStateClient2
   {
      
      private var _459811771_HScrollbarLeftButton_Group1:Group;
      
      private var _1578209128_HScrollbarLeftButton_SolidColorStroke1:SolidColorStroke;
      
      private var __moduleFactoryInitialized:Boolean = false;
      
      private var _213507019hostComponent:Button;
      
      public function HScrollbarLeftButton()
      {
         super();
         mx_internal::_document = this;
         this.width = 18;
         this.height = 13;
         this.mxmlContent = [this._HScrollbarLeftButton_Rect1_c(),this._HScrollbarLeftButton_Rect2_c(),this._HScrollbarLeftButton_Rect3_c(),this._HScrollbarLeftButton_Group1_i()];
         this.currentState = "up";
         states = [new State({
            "name":"up",
            "overrides":[]
         }),new State({
            "name":"over",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_HScrollbarLeftButton_Group1",
               "name":"alpha",
               "value":1
            })]
         }),new State({
            "name":"down",
            "overrides":[]
         }),new State({
            "name":"disabled",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_HScrollbarLeftButton_SolidColorStroke1",
               "name":"color",
               "value":3355443
            })]
         })];
      }
      
      override public function set moduleFactory(param1:IFlexModuleFactory) : void
      {
         super.moduleFactory = param1;
         if(this.__moduleFactoryInitialized)
         {
            return;
         }
         this.__moduleFactoryInitialized = true;
      }
      
      override public function initialize() : void
      {
         super.initialize();
      }
      
      private function _HScrollbarLeftButton_Rect1_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.x = 0.5;
         _loc1_.y = 0.5;
         _loc1_.width = 17;
         _loc1_.height = 12;
         _loc1_.stroke = this._HScrollbarLeftButton_SolidColorStroke1_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _HScrollbarLeftButton_SolidColorStroke1_i() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.alpha = 0.6;
         _loc1_.caps = "square";
         _loc1_.color = 0;
         _loc1_.joints = "miter";
         this._HScrollbarLeftButton_SolidColorStroke1 = _loc1_;
         BindingManager.executeBindings(this,"_HScrollbarLeftButton_SolidColorStroke1",this._HScrollbarLeftButton_SolidColorStroke1);
         return _loc1_;
      }
      
      private function _HScrollbarLeftButton_Rect2_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.x = 1.5;
         _loc1_.y = 1.5;
         _loc1_.width = 15;
         _loc1_.height = 10;
         _loc1_.stroke = this._HScrollbarLeftButton_SolidColorStroke2_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _HScrollbarLeftButton_SolidColorStroke2_c() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.alpha = 0.15;
         _loc1_.caps = "square";
         _loc1_.color = 16777215;
         _loc1_.joints = "miter";
         return _loc1_;
      }
      
      private function _HScrollbarLeftButton_Rect3_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.x = 2;
         _loc1_.y = 2;
         _loc1_.width = 14;
         _loc1_.height = 9;
         _loc1_.fill = this._HScrollbarLeftButton_SolidColor1_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _HScrollbarLeftButton_SolidColor1_c() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.alpha = 0.05;
         _loc1_.color = 16777215;
         return _loc1_;
      }
      
      private function _HScrollbarLeftButton_Group1_i() : Group
      {
         var _loc1_:Group = new Group();
         _loc1_.alpha = 0.5;
         _loc1_.horizontalCenter = -0.5;
         _loc1_.verticalCenter = 0;
         _loc1_.filters = [this._HScrollbarLeftButton_DropShadowFilter1_c()];
         _loc1_.mxmlContent = [this._HScrollbarLeftButton_Rect4_c(),this._HScrollbarLeftButton_Rect5_c(),this._HScrollbarLeftButton_Rect6_c()];
         _loc1_.id = "_HScrollbarLeftButton_Group1";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         this._HScrollbarLeftButton_Group1 = _loc1_;
         BindingManager.executeBindings(this,"_HScrollbarLeftButton_Group1",this._HScrollbarLeftButton_Group1);
         return _loc1_;
      }
      
      private function _HScrollbarLeftButton_DropShadowFilter1_c() : DropShadowFilter
      {
         var _loc1_:DropShadowFilter = new DropShadowFilter();
         _loc1_.alpha = 0.5;
         _loc1_.angle = 90;
         _loc1_.blurX = 1;
         _loc1_.blurY = 1;
         _loc1_.color = 0;
         _loc1_.distance = 1;
         _loc1_.hideObject = false;
         _loc1_.inner = false;
         _loc1_.knockout = false;
         _loc1_.quality = 2;
         _loc1_.strength = 4;
         return _loc1_;
      }
      
      private function _HScrollbarLeftButton_Rect4_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.x = 0;
         _loc1_.y = 2;
         _loc1_.width = 1;
         _loc1_.height = 1;
         _loc1_.fill = this._HScrollbarLeftButton_SolidColor2_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _HScrollbarLeftButton_SolidColor2_c() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 16777215;
         return _loc1_;
      }
      
      private function _HScrollbarLeftButton_Rect5_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.x = 1;
         _loc1_.y = 1;
         _loc1_.width = 1;
         _loc1_.height = 3;
         _loc1_.fill = this._HScrollbarLeftButton_SolidColor3_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _HScrollbarLeftButton_SolidColor3_c() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 16777215;
         return _loc1_;
      }
      
      private function _HScrollbarLeftButton_Rect6_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.x = 2;
         _loc1_.y = 0;
         _loc1_.width = 1;
         _loc1_.height = 5;
         _loc1_.fill = this._HScrollbarLeftButton_SolidColor4_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _HScrollbarLeftButton_SolidColor4_c() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 16777215;
         return _loc1_;
      }
      
      [Bindable(event="propertyChange")]
      public function get _HScrollbarLeftButton_Group1() : Group
      {
         return this._459811771_HScrollbarLeftButton_Group1;
      }
      
      public function set _HScrollbarLeftButton_Group1(param1:Group) : void
      {
         var _loc2_:Object = this._459811771_HScrollbarLeftButton_Group1;
         if(_loc2_ !== param1)
         {
            this._459811771_HScrollbarLeftButton_Group1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_HScrollbarLeftButton_Group1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _HScrollbarLeftButton_SolidColorStroke1() : SolidColorStroke
      {
         return this._1578209128_HScrollbarLeftButton_SolidColorStroke1;
      }
      
      public function set _HScrollbarLeftButton_SolidColorStroke1(param1:SolidColorStroke) : void
      {
         var _loc2_:Object = this._1578209128_HScrollbarLeftButton_SolidColorStroke1;
         if(_loc2_ !== param1)
         {
            this._1578209128_HScrollbarLeftButton_SolidColorStroke1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_HScrollbarLeftButton_SolidColorStroke1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get hostComponent() : Button
      {
         return this._213507019hostComponent;
      }
      
      public function set hostComponent(param1:Button) : void
      {
         var _loc2_:Object = this._213507019hostComponent;
         if(_loc2_ !== param1)
         {
            this._213507019hostComponent = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"hostComponent",_loc2_,param1));
            }
         }
      }
   }
}

