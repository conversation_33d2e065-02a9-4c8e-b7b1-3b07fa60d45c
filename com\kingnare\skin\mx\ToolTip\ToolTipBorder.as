package com.kingnare.skin.mx.ToolTip
{
   import flash.display.Graphics;
   import flash.filters.DropShadowFilter;
   import flash.geom.Matrix;
   import mx.core.EdgeMetrics;
   import mx.skins.RectangularBorder;
   
   public class ToolTipBorder extends RectangularBorder
   {
      
      private var _borderMetrics:EdgeMetrics;
      
      public function ToolTipBorder()
      {
         super();
         this.filters = [new DropShadowFilter(1.5,90,0,1,4,4,1.5)];
      }
      
      override public function get borderMetrics() : EdgeMetrics
      {
         if(this._borderMetrics)
         {
            return this._borderMetrics;
         }
         var _loc1_:String = getStyle("borderStyle");
         switch(_loc1_)
         {
            case "errorTipRight":
               this._borderMetrics = new EdgeMetrics(15,1,3,3);
               break;
            case "errorTipAbove":
               this._borderMetrics = new EdgeMetrics(3,1,3,15);
               break;
            case "errorTipBelow":
               this._borderMetrics = new EdgeMetrics(3,13,3,3);
               break;
            default:
               this._borderMetrics = new EdgeMetrics(3,1,3,3);
         }
         return this._borderMetrics;
      }
      
      override protected function updateDisplayList(param1:Number, param2:Number) : void
      {
         var _loc11_:Matrix = null;
         super.updateDisplayList(param1,param2);
         var _loc3_:uint = getStyle("backgroundColor");
         var _loc4_:uint = getStyle("borderColor");
         var _loc5_:uint = getStyle("borderAlpha");
         var _loc6_:String = getStyle("borderStyle");
         var _loc7_:uint = getStyle("shadowColor");
         var _loc8_:uint = getStyle("cornerRadius");
         var _loc9_:Number = 0;
         var _loc10_:Graphics = graphics;
         _loc10_.clear();
         switch(_loc6_)
         {
            case "toolTip":
               drawRoundRect(2,0,param1 - 4,param2 - 2,_loc8_,_loc4_,_loc5_);
               _loc11_ = new Matrix();
               _loc11_.createGradientBox(param1 - 6,param2 - 4,Math.PI / 2,0,0);
               drawRoundRect(3,1,param1 - 6,param2 - 4,_loc8_,[_loc3_,_loc3_],[0.9,0.9],_loc11_);
               break;
            case "errorTipRight":
               drawRoundRect(x + 11,y + 5,param1 - 11,param2 - 5,_loc8_,_loc7_,_loc9_);
               drawRoundRect(x + 13,y + 4,param1 - 13,param2 - 5,_loc8_,_loc7_,_loc9_);
               drawRoundRect(x + 11,y,param1 - 11,param2 - 2,_loc8_,_loc4_,1);
               drawRoundRect(x + 13,y + 1,param1 - 13,param2 - 4,_loc8_,_loc4_,1);
               _loc10_.beginFill(_loc7_,_loc9_);
               _loc10_.moveTo(x + 11,y + 7);
               _loc10_.lineTo(x,y + 14);
               _loc10_.lineTo(x + 11,y + 20);
               _loc10_.moveTo(x + 11,y + 8);
               _loc10_.endFill();
               _loc10_.beginFill(_loc4_);
               _loc10_.moveTo(x + 11,y + 7);
               _loc10_.lineTo(x,y + 13);
               _loc10_.lineTo(x + 11,y + 19);
               _loc10_.moveTo(x + 11,y + 7);
               _loc10_.endFill();
               break;
            case "errorTipAbove":
               drawRoundRect(x,y + 5,param1,param2 - 16,_loc8_,_loc7_,_loc9_);
               drawRoundRect(x + 1,y + 4,param1 - 2,param2 - 16,_loc8_,_loc7_,_loc9_);
               drawRoundRect(x,y,param1,param2 - 13,_loc8_,_loc4_,1);
               drawRoundRect(x + 1,y + 1,param1 - 2,param2 - 15,_loc8_,_loc4_,1);
               _loc10_.beginFill(_loc7_,_loc9_);
               _loc10_.moveTo(x + 9,y + param2 - 11);
               _loc10_.lineTo(x + 15,y + param2);
               _loc10_.lineTo(x + 21,y + param2 - 11);
               _loc10_.moveTo(x + 10,y + param2 - 11);
               _loc10_.endFill();
               _loc10_.beginFill(_loc4_);
               _loc10_.moveTo(x + 9,y + param2 - 13);
               _loc10_.lineTo(x + 15,y + param2 - 2);
               _loc10_.lineTo(x + 21,y + param2 - 13);
               _loc10_.moveTo(x + 9,y + param2 - 13);
               _loc10_.endFill();
               break;
            case "errorTipBelow":
               drawRoundRect(x,y + 16,param1,param2 - 16,_loc8_,_loc7_,_loc9_);
               drawRoundRect(x + 1,y + 15,param1 - 2,param2 - 16,_loc8_,_loc7_,_loc9_);
               drawRoundRect(x,y + 11,param1,param2 - 13,_loc8_,_loc4_,1);
               drawRoundRect(x + 1,y + 13,param1 - 2,param2 - 15,_loc8_,_loc4_,1);
               _loc10_.beginFill(_loc4_);
               _loc10_.moveTo(x + 9,y + 11);
               _loc10_.lineTo(x + 15,y);
               _loc10_.lineTo(x + 21,y + 11);
               _loc10_.moveTo(x + 10,y + 11);
               _loc10_.endFill();
         }
      }
      
      override public function styleChanged(param1:String) : void
      {
         if(param1 == "borderStyle" || param1 == "styleName")
         {
            this._borderMetrics = null;
         }
         invalidateDisplayList();
      }
   }
}

