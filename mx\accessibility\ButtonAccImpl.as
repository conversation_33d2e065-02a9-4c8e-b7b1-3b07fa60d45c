package mx.accessibility
{
   import flash.accessibility.Accessibility;
   import flash.events.Event;
   import flash.events.KeyboardEvent;
   import flash.ui.Keyboard;
   import mx.controls.Button;
   import mx.core.UIComponent;
   import mx.core.mx_internal;
   
   use namespace mx_internal;
   
   public class ButtonAccImpl extends AccImpl
   {
      
      mx_internal static const VERSION:String = "4.6.0.23201";
      
      public function ButtonAccImpl(param1:UIComponent)
      {
         super(param1);
         role = AccConst.ROLE_SYSTEM_PUSHBUTTON;
      }
      
      public static function enableAccessibility() : void
      {
         Button.mx_internal::createAccessibilityImplementation = mx_internal::createAccessibilityImplementation;
      }
      
      mx_internal static function createAccessibilityImplementation(param1:UIComponent) : void
      {
         param1.accessibilityImplementation = new ButtonAccImpl(param1);
      }
      
      override protected function get eventsToHandle() : Array
      {
         return super.eventsToHandle.concat(["click","labelChanged"]);
      }
      
      override public function get_accState(param1:uint) : uint
      {
         var _loc2_:uint = getState(param1);
         if(<PERSON><PERSON>(master).selected)
         {
            _loc2_ |= AccConst.STATE_SYSTEM_PRESSED;
         }
         return _loc2_;
      }
      
      override public function get_accDefaultAction(param1:uint) : String
      {
         return "Press";
      }
      
      override public function accDoDefaultAction(param1:uint) : void
      {
         var _loc2_:KeyboardEvent = null;
         if(master.enabled)
         {
            _loc2_ = new KeyboardEvent(KeyboardEvent.KEY_DOWN);
            _loc2_.keyCode = Keyboard.SPACE;
            master.dispatchEvent(_loc2_);
            _loc2_ = new KeyboardEvent(KeyboardEvent.KEY_UP);
            _loc2_.keyCode = Keyboard.SPACE;
            master.dispatchEvent(_loc2_);
         }
      }
      
      override protected function getName(param1:uint) : String
      {
         var _loc2_:String = Button(master).label;
         return _loc2_ != null && _loc2_ != "" ? _loc2_ : "";
      }
      
      override protected function eventHandler(param1:Event) : void
      {
         $eventHandler(param1);
         switch(param1.type)
         {
            case "click":
               Accessibility.sendEvent(master,0,AccConst.EVENT_OBJECT_STATECHANGE);
               Accessibility.updateProperties();
               break;
            case "labelChanged":
               Accessibility.sendEvent(master,0,AccConst.EVENT_OBJECT_NAMECHANGE);
               Accessibility.updateProperties();
         }
      }
   }
}

