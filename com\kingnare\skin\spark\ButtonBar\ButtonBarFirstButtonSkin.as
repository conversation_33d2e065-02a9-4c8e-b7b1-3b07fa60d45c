package com.kingnare.skin.spark.ButtonBar
{
   import mx.binding.BindingManager;
   import mx.core.DeferredInstanceFromFunction;
   import mx.core.IFlexModuleFactory;
   import mx.core.IStateClient2;
   import mx.events.PropertyChangeEvent;
   import mx.graphics.GradientEntry;
   import mx.graphics.LinearGradient;
   import mx.graphics.LinearGradientStroke;
   import mx.graphics.SolidColor;
   import mx.graphics.SolidColorStroke;
   import mx.states.AddItems;
   import mx.states.SetProperty;
   import mx.states.State;
   import spark.components.Group;
   import spark.components.Label;
   import spark.primitives.Rect;
   import spark.skins.SparkButtonSkin;
   
   public class ButtonBarFirstButtonSkin extends SparkButtonSkin implements IStateClient2
   {
      
      private static const exclusions:Array = ["labelDisplay"];
      
      private var _465750417_ButtonBarFirstButtonSkin_GradientEntry1:GradientEntry;
      
      private var _465750418_ButtonBarFirstButtonSkin_GradientEntry2:GradientEntry;
      
      private var _465750419_ButtonBarFirstButtonSkin_GradientEntry3:GradientEntry;
      
      private var _465750420_ButtonBarFirstButtonSkin_GradientEntry4:GradientEntry;
      
      private var _2068758572_ButtonBarFirstButtonSkin_Group1:Group;
      
      private var _1383304148border:Rect;
      
      private var _3143043fill:Rect;
      
      private var _728661292fillbase:Rect;
      
      private var _1507289076highlightStroke:Rect;
      
      private var _1306546406lowlightStroke:Rect;
      
      private var __moduleFactoryInitialized:Boolean = false;
      
      private var cornerRadius:Number = 2;
      
      public function ButtonBarFirstButtonSkin()
      {
         super();
         mx_internal::_document = this;
         this.minWidth = 21;
         this.minHeight = 21;
         this.mxmlContent = [this._ButtonBarFirstButtonSkin_Group1_i(),this._ButtonBarFirstButtonSkin_Label1_i()];
         this.currentState = "up";
         var _loc1_:DeferredInstanceFromFunction = new DeferredInstanceFromFunction(this._ButtonBarFirstButtonSkin_Rect4_i);
         states = [new State({
            "name":"up",
            "overrides":[]
         }),new State({
            "name":"over",
            "stateGroups":["overStates"],
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_ButtonBarFirstButtonSkin_GradientEntry1",
               "name":"alpha",
               "value":0.15
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarFirstButtonSkin_GradientEntry2",
               "name":"alpha",
               "value":0.05
            })]
         }),new State({
            "name":"down",
            "stateGroups":["downStates"],
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_ButtonBarFirstButtonSkin_GradientEntry1",
               "name":"alpha",
               "value":0.01
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarFirstButtonSkin_GradientEntry2",
               "name":"alpha",
               "value":0.01
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarFirstButtonSkin_GradientEntry3",
               "name":"alpha",
               "value":0.2
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarFirstButtonSkin_GradientEntry3",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarFirstButtonSkin_GradientEntry4",
               "name":"alpha",
               "value":0.15
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarFirstButtonSkin_GradientEntry4",
               "name":"color",
               "value":0
            })]
         }),new State({
            "name":"disabled",
            "stateGroups":["disabledStates"],
            "overrides":[new SetProperty().initializeFromObject({
               "name":"alpha",
               "value":0.5
            })]
         }),new State({
            "name":"upAndSelected",
            "stateGroups":["selectedUpStates","selectedStates"],
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_loc1_,
               "destination":"_ButtonBarFirstButtonSkin_Group1",
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["highlightStroke"]
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarFirstButtonSkin_GradientEntry1",
               "name":"alpha",
               "value":0.05
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarFirstButtonSkin_GradientEntry2",
               "name":"alpha",
               "value":0.05
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarFirstButtonSkin_GradientEntry3",
               "name":"alpha",
               "value":0.2
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarFirstButtonSkin_GradientEntry3",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarFirstButtonSkin_GradientEntry4",
               "name":"alpha",
               "value":0.15
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarFirstButtonSkin_GradientEntry4",
               "name":"color",
               "value":0
            })]
         }),new State({
            "name":"overAndSelected",
            "stateGroups":["overStates","selectedStates"],
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_loc1_,
               "destination":"_ButtonBarFirstButtonSkin_Group1",
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["highlightStroke"]
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarFirstButtonSkin_GradientEntry1",
               "name":"alpha",
               "value":0.08
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarFirstButtonSkin_GradientEntry2",
               "name":"alpha",
               "value":0.08
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarFirstButtonSkin_GradientEntry3",
               "name":"alpha",
               "value":0.2
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarFirstButtonSkin_GradientEntry3",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarFirstButtonSkin_GradientEntry4",
               "name":"alpha",
               "value":0.15
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarFirstButtonSkin_GradientEntry4",
               "name":"color",
               "value":0
            })]
         }),new State({
            "name":"downAndSelected",
            "stateGroups":["downStates","selectedStates"],
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_loc1_,
               "destination":"_ButtonBarFirstButtonSkin_Group1",
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["highlightStroke"]
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarFirstButtonSkin_GradientEntry1",
               "name":"alpha",
               "value":0.01
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarFirstButtonSkin_GradientEntry2",
               "name":"alpha",
               "value":0.01
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarFirstButtonSkin_GradientEntry3",
               "name":"alpha",
               "value":0.2
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarFirstButtonSkin_GradientEntry3",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarFirstButtonSkin_GradientEntry4",
               "name":"alpha",
               "value":0.15
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarFirstButtonSkin_GradientEntry4",
               "name":"color",
               "value":0
            })]
         }),new State({
            "name":"disabledAndSelected",
            "stateGroups":["disabledStates","selectedUpStates","selectedStates"],
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_loc1_,
               "destination":"_ButtonBarFirstButtonSkin_Group1",
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["highlightStroke"]
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarFirstButtonSkin_GradientEntry1",
               "name":"alpha",
               "value":0.05
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarFirstButtonSkin_GradientEntry2",
               "name":"alpha",
               "value":0.05
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarFirstButtonSkin_GradientEntry3",
               "name":"alpha",
               "value":0.2
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarFirstButtonSkin_GradientEntry3",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarFirstButtonSkin_GradientEntry4",
               "name":"alpha",
               "value":0.15
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarFirstButtonSkin_GradientEntry4",
               "name":"color",
               "value":0
            })]
         })];
      }
      
      override public function set moduleFactory(param1:IFlexModuleFactory) : void
      {
         super.moduleFactory = param1;
         if(this.__moduleFactoryInitialized)
         {
            return;
         }
         this.__moduleFactoryInitialized = true;
      }
      
      override public function initialize() : void
      {
         super.initialize();
      }
      
      override public function get colorizeExclusions() : Array
      {
         return exclusions;
      }
      
      override protected function initializationComplete() : void
      {
         useChromeColor = true;
         super.initializationComplete();
      }
      
      override protected function updateDisplayList(param1:Number, param2:Number) : void
      {
         var _loc3_:Number = getStyle("cornerRadius");
         if(this.cornerRadius != _loc3_)
         {
            this.cornerRadius = _loc3_;
            this.fill.topLeftRadiusX = this.cornerRadius;
            this.fill.bottomLeftRadiusX = this.cornerRadius;
            this.fillbase.topLeftRadiusX = this.cornerRadius;
            this.fillbase.bottomLeftRadiusX = this.cornerRadius;
            this.highlightStroke.topLeftRadiusX = this.cornerRadius;
            this.highlightStroke.bottomLeftRadiusX = this.cornerRadius;
            this.border.topLeftRadiusX = this.cornerRadius;
            this.border.bottomLeftRadiusX = this.cornerRadius;
         }
         if(this.lowlightStroke)
         {
            this.lowlightStroke.topLeftRadiusX = this.cornerRadius;
            this.lowlightStroke.bottomLeftRadiusX = this.cornerRadius;
         }
         super.updateDisplayList(param1,unscaledHeight);
      }
      
      private function _ButtonBarFirstButtonSkin_Group1_i() : Group
      {
         var _loc1_:Group = new Group();
         _loc1_.left = -1;
         _loc1_.right = 0;
         _loc1_.top = -1;
         _loc1_.bottom = -1;
         _loc1_.mxmlContent = [this._ButtonBarFirstButtonSkin_Rect1_i(),this._ButtonBarFirstButtonSkin_Rect2_i(),this._ButtonBarFirstButtonSkin_Rect3_i(),this._ButtonBarFirstButtonSkin_Rect5_i()];
         _loc1_.id = "_ButtonBarFirstButtonSkin_Group1";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         this._ButtonBarFirstButtonSkin_Group1 = _loc1_;
         BindingManager.executeBindings(this,"_ButtonBarFirstButtonSkin_Group1",this._ButtonBarFirstButtonSkin_Group1);
         return _loc1_;
      }
      
      private function _ButtonBarFirstButtonSkin_Rect1_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 0;
         _loc1_.right = 0;
         _loc1_.top = 0;
         _loc1_.bottom = 0;
         _loc1_.topLeftRadiusX = 2;
         _loc1_.bottomLeftRadiusX = 2;
         _loc1_.fill = this._ButtonBarFirstButtonSkin_SolidColor1_c();
         _loc1_.initialized(this,"fillbase");
         this.fillbase = _loc1_;
         BindingManager.executeBindings(this,"fillbase",this.fillbase);
         return _loc1_;
      }
      
      private function _ButtonBarFirstButtonSkin_SolidColor1_c() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 3355443;
         return _loc1_;
      }
      
      private function _ButtonBarFirstButtonSkin_Rect2_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 2;
         _loc1_.right = 1;
         _loc1_.top = 2;
         _loc1_.bottom = 2;
         _loc1_.topLeftRadiusX = 2;
         _loc1_.bottomLeftRadiusX = 2;
         _loc1_.fill = this._ButtonBarFirstButtonSkin_LinearGradient1_c();
         _loc1_.initialized(this,"fill");
         this.fill = _loc1_;
         BindingManager.executeBindings(this,"fill",this.fill);
         return _loc1_;
      }
      
      private function _ButtonBarFirstButtonSkin_LinearGradient1_c() : LinearGradient
      {
         var _loc1_:LinearGradient = new LinearGradient();
         _loc1_.rotation = 90;
         _loc1_.entries = [this._ButtonBarFirstButtonSkin_GradientEntry1_i(),this._ButtonBarFirstButtonSkin_GradientEntry2_i()];
         return _loc1_;
      }
      
      private function _ButtonBarFirstButtonSkin_GradientEntry1_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 16777215;
         _loc1_.alpha = 0.08;
         this._ButtonBarFirstButtonSkin_GradientEntry1 = _loc1_;
         BindingManager.executeBindings(this,"_ButtonBarFirstButtonSkin_GradientEntry1",this._ButtonBarFirstButtonSkin_GradientEntry1);
         return _loc1_;
      }
      
      private function _ButtonBarFirstButtonSkin_GradientEntry2_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 16777215;
         _loc1_.alpha = 0.03;
         this._ButtonBarFirstButtonSkin_GradientEntry2 = _loc1_;
         BindingManager.executeBindings(this,"_ButtonBarFirstButtonSkin_GradientEntry2",this._ButtonBarFirstButtonSkin_GradientEntry2);
         return _loc1_;
      }
      
      private function _ButtonBarFirstButtonSkin_Rect3_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 2;
         _loc1_.right = 1;
         _loc1_.top = 2;
         _loc1_.bottom = 2;
         _loc1_.topLeftRadiusX = 2;
         _loc1_.bottomLeftRadiusX = 2;
         _loc1_.stroke = this._ButtonBarFirstButtonSkin_LinearGradientStroke1_c();
         _loc1_.initialized(this,"highlightStroke");
         this.highlightStroke = _loc1_;
         BindingManager.executeBindings(this,"highlightStroke",this.highlightStroke);
         return _loc1_;
      }
      
      private function _ButtonBarFirstButtonSkin_LinearGradientStroke1_c() : LinearGradientStroke
      {
         var _loc1_:LinearGradientStroke = new LinearGradientStroke();
         _loc1_.rotation = 90;
         _loc1_.weight = 1;
         _loc1_.entries = [this._ButtonBarFirstButtonSkin_GradientEntry3_i(),this._ButtonBarFirstButtonSkin_GradientEntry4_i()];
         return _loc1_;
      }
      
      private function _ButtonBarFirstButtonSkin_GradientEntry3_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 16777215;
         _loc1_.alpha = 0.1;
         this._ButtonBarFirstButtonSkin_GradientEntry3 = _loc1_;
         BindingManager.executeBindings(this,"_ButtonBarFirstButtonSkin_GradientEntry3",this._ButtonBarFirstButtonSkin_GradientEntry3);
         return _loc1_;
      }
      
      private function _ButtonBarFirstButtonSkin_GradientEntry4_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 16777215;
         _loc1_.alpha = 0.03;
         this._ButtonBarFirstButtonSkin_GradientEntry4 = _loc1_;
         BindingManager.executeBindings(this,"_ButtonBarFirstButtonSkin_GradientEntry4",this._ButtonBarFirstButtonSkin_GradientEntry4);
         return _loc1_;
      }
      
      private function _ButtonBarFirstButtonSkin_Rect4_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 3;
         _loc1_.right = 2;
         _loc1_.top = 3;
         _loc1_.bottom = 3;
         _loc1_.topLeftRadiusX = 2;
         _loc1_.bottomLeftRadiusX = 2;
         _loc1_.stroke = this._ButtonBarFirstButtonSkin_LinearGradientStroke2_c();
         _loc1_.initialized(this,"lowlightStroke");
         this.lowlightStroke = _loc1_;
         BindingManager.executeBindings(this,"lowlightStroke",this.lowlightStroke);
         return _loc1_;
      }
      
      private function _ButtonBarFirstButtonSkin_LinearGradientStroke2_c() : LinearGradientStroke
      {
         var _loc1_:LinearGradientStroke = new LinearGradientStroke();
         _loc1_.rotation = 90;
         _loc1_.weight = 1;
         _loc1_.entries = [this._ButtonBarFirstButtonSkin_GradientEntry5_c(),this._ButtonBarFirstButtonSkin_GradientEntry6_c()];
         return _loc1_;
      }
      
      private function _ButtonBarFirstButtonSkin_GradientEntry5_c() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 0;
         _loc1_.alpha = 0.08;
         return _loc1_;
      }
      
      private function _ButtonBarFirstButtonSkin_GradientEntry6_c() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 0;
         _loc1_.alpha = 0.03;
         return _loc1_;
      }
      
      private function _ButtonBarFirstButtonSkin_Rect5_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 1;
         _loc1_.right = 0;
         _loc1_.top = 1;
         _loc1_.bottom = 1;
         _loc1_.topLeftRadiusX = 2;
         _loc1_.bottomLeftRadiusX = 2;
         _loc1_.stroke = this._ButtonBarFirstButtonSkin_SolidColorStroke1_c();
         _loc1_.initialized(this,"border");
         this.border = _loc1_;
         BindingManager.executeBindings(this,"border",this.border);
         return _loc1_;
      }
      
      private function _ButtonBarFirstButtonSkin_SolidColorStroke1_c() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.color = 0;
         return _loc1_;
      }
      
      private function _ButtonBarFirstButtonSkin_Label1_i() : Label
      {
         var _loc1_:Label = new Label();
         _loc1_.maxDisplayedLines = 1;
         _loc1_.horizontalCenter = 0;
         _loc1_.verticalCenter = 1;
         _loc1_.left = 10;
         _loc1_.right = 10;
         _loc1_.top = 2;
         _loc1_.bottom = 2;
         _loc1_.setStyle("textAlign","center");
         _loc1_.setStyle("verticalAlign","middle");
         _loc1_.id = "labelDisplay";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         labelDisplay = _loc1_;
         BindingManager.executeBindings(this,"labelDisplay",labelDisplay);
         return _loc1_;
      }
      
      [Bindable(event="propertyChange")]
      public function get _ButtonBarFirstButtonSkin_GradientEntry1() : GradientEntry
      {
         return this._465750417_ButtonBarFirstButtonSkin_GradientEntry1;
      }
      
      public function set _ButtonBarFirstButtonSkin_GradientEntry1(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._465750417_ButtonBarFirstButtonSkin_GradientEntry1;
         if(_loc2_ !== param1)
         {
            this._465750417_ButtonBarFirstButtonSkin_GradientEntry1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_ButtonBarFirstButtonSkin_GradientEntry1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _ButtonBarFirstButtonSkin_GradientEntry2() : GradientEntry
      {
         return this._465750418_ButtonBarFirstButtonSkin_GradientEntry2;
      }
      
      public function set _ButtonBarFirstButtonSkin_GradientEntry2(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._465750418_ButtonBarFirstButtonSkin_GradientEntry2;
         if(_loc2_ !== param1)
         {
            this._465750418_ButtonBarFirstButtonSkin_GradientEntry2 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_ButtonBarFirstButtonSkin_GradientEntry2",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _ButtonBarFirstButtonSkin_GradientEntry3() : GradientEntry
      {
         return this._465750419_ButtonBarFirstButtonSkin_GradientEntry3;
      }
      
      public function set _ButtonBarFirstButtonSkin_GradientEntry3(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._465750419_ButtonBarFirstButtonSkin_GradientEntry3;
         if(_loc2_ !== param1)
         {
            this._465750419_ButtonBarFirstButtonSkin_GradientEntry3 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_ButtonBarFirstButtonSkin_GradientEntry3",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _ButtonBarFirstButtonSkin_GradientEntry4() : GradientEntry
      {
         return this._465750420_ButtonBarFirstButtonSkin_GradientEntry4;
      }
      
      public function set _ButtonBarFirstButtonSkin_GradientEntry4(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._465750420_ButtonBarFirstButtonSkin_GradientEntry4;
         if(_loc2_ !== param1)
         {
            this._465750420_ButtonBarFirstButtonSkin_GradientEntry4 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_ButtonBarFirstButtonSkin_GradientEntry4",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _ButtonBarFirstButtonSkin_Group1() : Group
      {
         return this._2068758572_ButtonBarFirstButtonSkin_Group1;
      }
      
      public function set _ButtonBarFirstButtonSkin_Group1(param1:Group) : void
      {
         var _loc2_:Object = this._2068758572_ButtonBarFirstButtonSkin_Group1;
         if(_loc2_ !== param1)
         {
            this._2068758572_ButtonBarFirstButtonSkin_Group1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_ButtonBarFirstButtonSkin_Group1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get border() : Rect
      {
         return this._1383304148border;
      }
      
      public function set border(param1:Rect) : void
      {
         var _loc2_:Object = this._1383304148border;
         if(_loc2_ !== param1)
         {
            this._1383304148border = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"border",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get fill() : Rect
      {
         return this._3143043fill;
      }
      
      public function set fill(param1:Rect) : void
      {
         var _loc2_:Object = this._3143043fill;
         if(_loc2_ !== param1)
         {
            this._3143043fill = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"fill",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get fillbase() : Rect
      {
         return this._728661292fillbase;
      }
      
      public function set fillbase(param1:Rect) : void
      {
         var _loc2_:Object = this._728661292fillbase;
         if(_loc2_ !== param1)
         {
            this._728661292fillbase = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"fillbase",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get highlightStroke() : Rect
      {
         return this._1507289076highlightStroke;
      }
      
      public function set highlightStroke(param1:Rect) : void
      {
         var _loc2_:Object = this._1507289076highlightStroke;
         if(_loc2_ !== param1)
         {
            this._1507289076highlightStroke = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"highlightStroke",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get lowlightStroke() : Rect
      {
         return this._1306546406lowlightStroke;
      }
      
      public function set lowlightStroke(param1:Rect) : void
      {
         var _loc2_:Object = this._1306546406lowlightStroke;
         if(_loc2_ !== param1)
         {
            this._1306546406lowlightStroke = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"lowlightStroke",_loc2_,param1));
            }
         }
      }
   }
}

