package com.kingnare.skin.spark.RadioButton
{
   import mx.binding.BindingManager;
   import mx.core.DeferredInstanceFromFunction;
   import mx.core.IFlexModuleFactory;
   import mx.core.IStateClient2;
   import mx.events.PropertyChangeEvent;
   import mx.graphics.GradientEntry;
   import mx.graphics.LinearGradient;
   import mx.graphics.LinearGradientStroke;
   import mx.graphics.SolidColor;
   import mx.graphics.SolidColorStroke;
   import mx.states.AddItems;
   import mx.states.SetProperty;
   import mx.states.State;
   import spark.components.Group;
   import spark.components.RadioButton;
   import spark.components.RichText;
   import spark.components.supportClasses.Skin;
   import spark.primitives.Ellipse;
   import spark.primitives.Rect;
   
   public class RadioButton extends Skin implements IStateClient2
   {
      
      public var _RadioButton_Ellipse4:Ellipse;
      
      private var _2065447072_RadioButton_GradientEntry1:GradientEntry;
      
      private var _2065447073_RadioButton_GradientEntry2:GradientEntry;
      
      private var _2065447074_RadioButton_GradientEntry3:GradientEntry;
      
      private var _2065447075_RadioButton_GradientEntry4:GradientEntry;
      
      private var _120181277_RadioButton_Group1:Group;
      
      public var _RadioButton_Group2:Group;
      
      private var _1254950256_RadioButton_SolidColorStroke1:SolidColorStroke;
      
      private var _1254950257_RadioButton_SolidColorStroke2:SolidColorStroke;
      
      private var _1184053038labelDisplay:RichText;
      
      private var __moduleFactoryInitialized:Boolean = false;
      
      private var _213507019hostComponent:spark.components.RadioButton;
      
      public function RadioButton()
      {
         super();
         mx_internal::_document = this;
         this.width = 123;
         this.height = 15;
         this.mxmlContent = [this._RadioButton_Group1_i(),this._RadioButton_RichText1_i()];
         this.currentState = "up";
         var _loc1_:DeferredInstanceFromFunction = new DeferredInstanceFromFunction(this._RadioButton_Group2_i);
         states = [new State({
            "name":"up",
            "overrides":[]
         }),new State({
            "name":"over",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_RadioButton_SolidColorStroke2",
               "name":"alpha",
               "value":0.05
            })]
         }),new State({
            "name":"down",
            "overrides":[]
         }),new State({
            "name":"disabled",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_RadioButton_SolidColorStroke1",
               "name":"color",
               "value":3355443
            }),new SetProperty().initializeFromObject({
               "target":"labelDisplay",
               "name":"alpha",
               "value":0.5
            })]
         }),new State({
            "name":"upAndSelected",
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_loc1_,
               "destination":"_RadioButton_Group1",
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["_RadioButton_Ellipse4"]
            }),new SetProperty().initializeFromObject({
               "target":"_RadioButton_GradientEntry1",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_RadioButton_GradientEntry2",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_RadioButton_GradientEntry3",
               "name":"alpha",
               "value":0.08
            }),new SetProperty().initializeFromObject({
               "target":"_RadioButton_GradientEntry3",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_RadioButton_GradientEntry4",
               "name":"alpha",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_RadioButton_GradientEntry4",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_RadioButton_SolidColorStroke1",
               "name":"alpha",
               "value":0.7000000000000001
            }),new SetProperty().initializeFromObject({
               "target":"_RadioButton_SolidColorStroke2",
               "name":"alpha",
               "value":0.1
            })]
         }),new State({
            "name":"overAndSelected",
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_loc1_,
               "destination":"_RadioButton_Group1",
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["_RadioButton_Ellipse4"]
            }),new SetProperty().initializeFromObject({
               "target":"_RadioButton_GradientEntry1",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_RadioButton_GradientEntry2",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_RadioButton_GradientEntry3",
               "name":"alpha",
               "value":0.08
            }),new SetProperty().initializeFromObject({
               "target":"_RadioButton_GradientEntry3",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_RadioButton_GradientEntry4",
               "name":"alpha",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_RadioButton_GradientEntry4",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_RadioButton_SolidColorStroke1",
               "name":"alpha",
               "value":0.7000000000000001
            }),new SetProperty().initializeFromObject({
               "target":"_RadioButton_SolidColorStroke2",
               "name":"alpha",
               "value":0.1
            })]
         }),new State({
            "name":"downAndSelected",
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_loc1_,
               "destination":"_RadioButton_Group1",
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["_RadioButton_Ellipse4"]
            }),new SetProperty().initializeFromObject({
               "target":"_RadioButton_GradientEntry1",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_RadioButton_GradientEntry2",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_RadioButton_GradientEntry3",
               "name":"alpha",
               "value":0.08
            }),new SetProperty().initializeFromObject({
               "target":"_RadioButton_GradientEntry3",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_RadioButton_GradientEntry4",
               "name":"alpha",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_RadioButton_GradientEntry4",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_RadioButton_SolidColorStroke1",
               "name":"alpha",
               "value":0.7000000000000001
            }),new SetProperty().initializeFromObject({
               "target":"_RadioButton_SolidColorStroke2",
               "name":"alpha",
               "value":0.1
            })]
         }),new State({
            "name":"disabledAndSelected",
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_loc1_,
               "destination":"_RadioButton_Group1",
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["_RadioButton_Ellipse4"]
            }),new SetProperty().initializeFromObject({
               "target":"_RadioButton_GradientEntry1",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_RadioButton_GradientEntry2",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_RadioButton_GradientEntry3",
               "name":"alpha",
               "value":0.08
            }),new SetProperty().initializeFromObject({
               "target":"_RadioButton_GradientEntry3",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_RadioButton_GradientEntry4",
               "name":"alpha",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_RadioButton_GradientEntry4",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_RadioButton_SolidColorStroke1",
               "name":"alpha",
               "value":0.7000000000000001
            }),new SetProperty().initializeFromObject({
               "target":"_RadioButton_SolidColorStroke1",
               "name":"color",
               "value":3355443
            }),new SetProperty().initializeFromObject({
               "target":"_RadioButton_SolidColorStroke2",
               "name":"alpha",
               "value":0.1
            }),new SetProperty().initializeFromObject({
               "target":"labelDisplay",
               "name":"alpha",
               "value":0.5
            })]
         })];
      }
      
      override public function set moduleFactory(param1:IFlexModuleFactory) : void
      {
         super.moduleFactory = param1;
         if(this.__moduleFactoryInitialized)
         {
            return;
         }
         this.__moduleFactoryInitialized = true;
      }
      
      override public function initialize() : void
      {
         super.initialize();
      }
      
      private function _RadioButton_Group1_i() : Group
      {
         var _loc1_:Group = new Group();
         _loc1_.x = 0;
         _loc1_.bottom = 0;
         _loc1_.mxmlContent = [this._RadioButton_Ellipse1_c(),this._RadioButton_Ellipse2_c(),this._RadioButton_Ellipse3_c(),this._RadioButton_Ellipse4_i()];
         _loc1_.id = "_RadioButton_Group1";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         this._RadioButton_Group1 = _loc1_;
         BindingManager.executeBindings(this,"_RadioButton_Group1",this._RadioButton_Group1);
         return _loc1_;
      }
      
      private function _RadioButton_Ellipse1_c() : Ellipse
      {
         var _loc1_:Ellipse = new Ellipse();
         _loc1_.x = 2;
         _loc1_.y = 2;
         _loc1_.width = 11;
         _loc1_.height = 11;
         _loc1_.alpha = 1;
         _loc1_.fill = this._RadioButton_LinearGradient1_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _RadioButton_LinearGradient1_c() : LinearGradient
      {
         var _loc1_:LinearGradient = new LinearGradient();
         _loc1_.rotation = 90;
         _loc1_.entries = [this._RadioButton_GradientEntry1_i(),this._RadioButton_GradientEntry2_i()];
         return _loc1_;
      }
      
      private function _RadioButton_GradientEntry1_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.alpha = 0.2;
         _loc1_.color = 0;
         _loc1_.ratio = 0;
         this._RadioButton_GradientEntry1 = _loc1_;
         BindingManager.executeBindings(this,"_RadioButton_GradientEntry1",this._RadioButton_GradientEntry1);
         return _loc1_;
      }
      
      private function _RadioButton_GradientEntry2_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.alpha = 0.03;
         _loc1_.color = 0;
         _loc1_.ratio = 1;
         this._RadioButton_GradientEntry2 = _loc1_;
         BindingManager.executeBindings(this,"_RadioButton_GradientEntry2",this._RadioButton_GradientEntry2);
         return _loc1_;
      }
      
      private function _RadioButton_Ellipse2_c() : Ellipse
      {
         var _loc1_:Ellipse = new Ellipse();
         _loc1_.x = 2.5;
         _loc1_.y = 2.5;
         _loc1_.width = 10;
         _loc1_.height = 10;
         _loc1_.alpha = 1;
         _loc1_.stroke = this._RadioButton_LinearGradientStroke1_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _RadioButton_LinearGradientStroke1_c() : LinearGradientStroke
      {
         var _loc1_:LinearGradientStroke = new LinearGradientStroke();
         _loc1_.caps = "none";
         _loc1_.joints = "miter";
         _loc1_.miterLimit = 4;
         _loc1_.rotation = 90;
         _loc1_.weight = 1;
         _loc1_.entries = [this._RadioButton_GradientEntry3_i(),this._RadioButton_GradientEntry4_i()];
         return _loc1_;
      }
      
      private function _RadioButton_GradientEntry3_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.alpha = 0.25;
         _loc1_.color = 0;
         _loc1_.ratio = 0;
         this._RadioButton_GradientEntry3 = _loc1_;
         BindingManager.executeBindings(this,"_RadioButton_GradientEntry3",this._RadioButton_GradientEntry3);
         return _loc1_;
      }
      
      private function _RadioButton_GradientEntry4_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.alpha = 0.1;
         _loc1_.color = 0;
         _loc1_.ratio = 1;
         this._RadioButton_GradientEntry4 = _loc1_;
         BindingManager.executeBindings(this,"_RadioButton_GradientEntry4",this._RadioButton_GradientEntry4);
         return _loc1_;
      }
      
      private function _RadioButton_Ellipse3_c() : Ellipse
      {
         var _loc1_:Ellipse = new Ellipse();
         _loc1_.x = 1.5;
         _loc1_.y = 1.5;
         _loc1_.width = 12;
         _loc1_.height = 12;
         _loc1_.stroke = this._RadioButton_SolidColorStroke1_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _RadioButton_SolidColorStroke1_i() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.alpha = 0.8;
         _loc1_.color = 0;
         this._RadioButton_SolidColorStroke1 = _loc1_;
         BindingManager.executeBindings(this,"_RadioButton_SolidColorStroke1",this._RadioButton_SolidColorStroke1);
         return _loc1_;
      }
      
      private function _RadioButton_Ellipse4_i() : Ellipse
      {
         var _loc1_:Ellipse = new Ellipse();
         _loc1_.x = 0.5;
         _loc1_.y = 0.5;
         _loc1_.width = 14;
         _loc1_.height = 14;
         _loc1_.stroke = this._RadioButton_SolidColorStroke2_i();
         _loc1_.initialized(this,"_RadioButton_Ellipse4");
         this._RadioButton_Ellipse4 = _loc1_;
         BindingManager.executeBindings(this,"_RadioButton_Ellipse4",this._RadioButton_Ellipse4);
         return _loc1_;
      }
      
      private function _RadioButton_SolidColorStroke2_i() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.alpha = 0.05;
         _loc1_.color = 16777215;
         this._RadioButton_SolidColorStroke2 = _loc1_;
         BindingManager.executeBindings(this,"_RadioButton_SolidColorStroke2",this._RadioButton_SolidColorStroke2);
         return _loc1_;
      }
      
      private function _RadioButton_Group2_i() : Group
      {
         var _loc1_:Group = new Group();
         _loc1_.x = 6;
         _loc1_.y = 6;
         _loc1_.mxmlContent = [this._RadioButton_Group3_c(),this._RadioButton_Group4_c(),this._RadioButton_Group5_c()];
         _loc1_.id = "_RadioButton_Group2";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         this._RadioButton_Group2 = _loc1_;
         BindingManager.executeBindings(this,"_RadioButton_Group2",this._RadioButton_Group2);
         return _loc1_;
      }
      
      private function _RadioButton_Group3_c() : Group
      {
         var _loc1_:Group = new Group();
         _loc1_.x = 0;
         _loc1_.y = 0;
         _loc1_.alpha = 0.9;
         _loc1_.mxmlContent = [this._RadioButton_Rect1_c(),this._RadioButton_Rect2_c()];
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         return _loc1_;
      }
      
      private function _RadioButton_Rect1_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.x = 0;
         _loc1_.y = 1;
         _loc1_.width = 3;
         _loc1_.height = 1;
         _loc1_.fill = this._RadioButton_SolidColor1_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _RadioButton_SolidColor1_c() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.alpha = 1;
         _loc1_.color = 16777215;
         return _loc1_;
      }
      
      private function _RadioButton_Rect2_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.x = 0;
         _loc1_.y = 1;
         _loc1_.width = 3;
         _loc1_.height = 1;
         _loc1_.rotation = 90;
         _loc1_.transformX = 1.5;
         _loc1_.transformY = 0.5;
         _loc1_.fill = this._RadioButton_SolidColor2_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _RadioButton_SolidColor2_c() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.alpha = 1;
         _loc1_.color = 16777215;
         return _loc1_;
      }
      
      private function _RadioButton_Group4_c() : Group
      {
         var _loc1_:Group = new Group();
         _loc1_.x = 0;
         _loc1_.y = 0;
         _loc1_.alpha = 0.35000000000000003;
         _loc1_.mxmlContent = [this._RadioButton_Rect3_c(),this._RadioButton_Rect4_c(),this._RadioButton_Rect5_c(),this._RadioButton_Rect6_c()];
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         return _loc1_;
      }
      
      private function _RadioButton_Rect3_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.x = 0;
         _loc1_.y = 0;
         _loc1_.width = 1;
         _loc1_.height = 1;
         _loc1_.fill = this._RadioButton_SolidColor3_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _RadioButton_SolidColor3_c() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 16777215;
         return _loc1_;
      }
      
      private function _RadioButton_Rect4_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.x = 2;
         _loc1_.y = 0;
         _loc1_.width = 1;
         _loc1_.height = 1;
         _loc1_.fill = this._RadioButton_SolidColor4_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _RadioButton_SolidColor4_c() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 16777215;
         return _loc1_;
      }
      
      private function _RadioButton_Rect5_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.x = 2;
         _loc1_.y = 2;
         _loc1_.width = 1;
         _loc1_.height = 1;
         _loc1_.fill = this._RadioButton_SolidColor5_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _RadioButton_SolidColor5_c() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 16777215;
         return _loc1_;
      }
      
      private function _RadioButton_Rect6_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.x = 0;
         _loc1_.y = 2;
         _loc1_.width = 1;
         _loc1_.height = 1;
         _loc1_.fill = this._RadioButton_SolidColor6_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _RadioButton_SolidColor6_c() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 16777215;
         return _loc1_;
      }
      
      private function _RadioButton_Group5_c() : Group
      {
         var _loc1_:Group = new Group();
         _loc1_.x = 0;
         _loc1_.y = 3;
         _loc1_.mxmlContent = [this._RadioButton_Rect7_c(),this._RadioButton_Rect8_c(),this._RadioButton_Rect9_c()];
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         return _loc1_;
      }
      
      private function _RadioButton_Rect7_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.x = 0;
         _loc1_.y = 0;
         _loc1_.width = 1;
         _loc1_.height = 1;
         _loc1_.fill = this._RadioButton_SolidColor7_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _RadioButton_SolidColor7_c() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.alpha = 0.15;
         _loc1_.color = 0;
         return _loc1_;
      }
      
      private function _RadioButton_Rect8_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.x = 2;
         _loc1_.y = 0;
         _loc1_.width = 1;
         _loc1_.height = 1;
         _loc1_.fill = this._RadioButton_SolidColor8_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _RadioButton_SolidColor8_c() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.alpha = 0.15;
         _loc1_.color = 0;
         return _loc1_;
      }
      
      private function _RadioButton_Rect9_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.x = 1;
         _loc1_.y = 0;
         _loc1_.width = 1;
         _loc1_.height = 1;
         _loc1_.fill = this._RadioButton_SolidColor9_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _RadioButton_SolidColor9_c() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.alpha = 0.3;
         _loc1_.color = 0;
         return _loc1_;
      }
      
      private function _RadioButton_RichText1_i() : RichText
      {
         var _loc1_:RichText = new RichText();
         _loc1_.x = 18;
         _loc1_.verticalCenter = 0;
         _loc1_.setStyle("textAlign","start");
         _loc1_.setStyle("verticalAlign","middle");
         _loc1_.id = "labelDisplay";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         this.labelDisplay = _loc1_;
         BindingManager.executeBindings(this,"labelDisplay",this.labelDisplay);
         return _loc1_;
      }
      
      [Bindable(event="propertyChange")]
      public function get _RadioButton_GradientEntry1() : GradientEntry
      {
         return this._2065447072_RadioButton_GradientEntry1;
      }
      
      public function set _RadioButton_GradientEntry1(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._2065447072_RadioButton_GradientEntry1;
         if(_loc2_ !== param1)
         {
            this._2065447072_RadioButton_GradientEntry1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_RadioButton_GradientEntry1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _RadioButton_GradientEntry2() : GradientEntry
      {
         return this._2065447073_RadioButton_GradientEntry2;
      }
      
      public function set _RadioButton_GradientEntry2(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._2065447073_RadioButton_GradientEntry2;
         if(_loc2_ !== param1)
         {
            this._2065447073_RadioButton_GradientEntry2 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_RadioButton_GradientEntry2",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _RadioButton_GradientEntry3() : GradientEntry
      {
         return this._2065447074_RadioButton_GradientEntry3;
      }
      
      public function set _RadioButton_GradientEntry3(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._2065447074_RadioButton_GradientEntry3;
         if(_loc2_ !== param1)
         {
            this._2065447074_RadioButton_GradientEntry3 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_RadioButton_GradientEntry3",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _RadioButton_GradientEntry4() : GradientEntry
      {
         return this._2065447075_RadioButton_GradientEntry4;
      }
      
      public function set _RadioButton_GradientEntry4(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._2065447075_RadioButton_GradientEntry4;
         if(_loc2_ !== param1)
         {
            this._2065447075_RadioButton_GradientEntry4 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_RadioButton_GradientEntry4",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _RadioButton_Group1() : Group
      {
         return this._120181277_RadioButton_Group1;
      }
      
      public function set _RadioButton_Group1(param1:Group) : void
      {
         var _loc2_:Object = this._120181277_RadioButton_Group1;
         if(_loc2_ !== param1)
         {
            this._120181277_RadioButton_Group1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_RadioButton_Group1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _RadioButton_SolidColorStroke1() : SolidColorStroke
      {
         return this._1254950256_RadioButton_SolidColorStroke1;
      }
      
      public function set _RadioButton_SolidColorStroke1(param1:SolidColorStroke) : void
      {
         var _loc2_:Object = this._1254950256_RadioButton_SolidColorStroke1;
         if(_loc2_ !== param1)
         {
            this._1254950256_RadioButton_SolidColorStroke1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_RadioButton_SolidColorStroke1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _RadioButton_SolidColorStroke2() : SolidColorStroke
      {
         return this._1254950257_RadioButton_SolidColorStroke2;
      }
      
      public function set _RadioButton_SolidColorStroke2(param1:SolidColorStroke) : void
      {
         var _loc2_:Object = this._1254950257_RadioButton_SolidColorStroke2;
         if(_loc2_ !== param1)
         {
            this._1254950257_RadioButton_SolidColorStroke2 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_RadioButton_SolidColorStroke2",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get labelDisplay() : RichText
      {
         return this._1184053038labelDisplay;
      }
      
      public function set labelDisplay(param1:RichText) : void
      {
         var _loc2_:Object = this._1184053038labelDisplay;
         if(_loc2_ !== param1)
         {
            this._1184053038labelDisplay = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"labelDisplay",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get hostComponent() : spark.components.RadioButton
      {
         return this._213507019hostComponent;
      }
      
      public function set hostComponent(param1:spark.components.RadioButton) : void
      {
         var _loc2_:Object = this._213507019hostComponent;
         if(_loc2_ !== param1)
         {
            this._213507019hostComponent = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"hostComponent",_loc2_,param1));
            }
         }
      }
   }
}

