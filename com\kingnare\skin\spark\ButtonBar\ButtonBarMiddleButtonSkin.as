package com.kingnare.skin.spark.ButtonBar
{
   import mx.binding.BindingManager;
   import mx.core.DeferredInstanceFromFunction;
   import mx.core.IFlexModuleFactory;
   import mx.core.IStateClient2;
   import mx.events.PropertyChangeEvent;
   import mx.graphics.GradientEntry;
   import mx.graphics.LinearGradient;
   import mx.graphics.LinearGradientStroke;
   import mx.graphics.SolidColor;
   import mx.graphics.SolidColorStroke;
   import mx.states.AddItems;
   import mx.states.SetProperty;
   import mx.states.State;
   import spark.components.Label;
   import spark.primitives.Rect;
   import spark.skins.SparkButtonSkin;
   
   public class ButtonBarMiddleButtonSkin extends SparkButtonSkin implements IStateClient2
   {
      
      private static const exclusions:Array = ["labelDisplay"];
      
      private var _1669602936_ButtonBarMiddleButtonSkin_GradientEntry1:GradientEntry;
      
      private var _1669602935_ButtonBarMiddleButtonSkin_GradientEntry2:GradientEntry;
      
      private var _1669602934_ButtonBarMiddleButtonSkin_GradientEntry3:GradientEntry;
      
      private var _1669602933_ButtonBarMiddleButtonSkin_GradientEntry4:GradientEntry;
      
      public var _ButtonBarMiddleButtonSkin_Rect2:Rect;
      
      public var _ButtonBarMiddleButtonSkin_Rect3:Rect;
      
      private var _728661292fillbase:Rect;
      
      private var _1306546406lowlightStroke:Rect;
      
      private var __moduleFactoryInitialized:Boolean = false;
      
      public function ButtonBarMiddleButtonSkin()
      {
         super();
         mx_internal::_document = this;
         this.minWidth = 21;
         this.minHeight = 21;
         this.mxmlContent = [this._ButtonBarMiddleButtonSkin_Rect1_i(),this._ButtonBarMiddleButtonSkin_Rect2_i(),this._ButtonBarMiddleButtonSkin_Rect5_c(),this._ButtonBarMiddleButtonSkin_Label1_i()];
         this.currentState = "up";
         var _loc1_:DeferredInstanceFromFunction = new DeferredInstanceFromFunction(this._ButtonBarMiddleButtonSkin_Rect3_i);
         var _loc2_:DeferredInstanceFromFunction = new DeferredInstanceFromFunction(this._ButtonBarMiddleButtonSkin_Rect4_i);
         states = [new State({
            "name":"up",
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_loc1_,
               "destination":null,
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["_ButtonBarMiddleButtonSkin_Rect2"]
            })]
         }),new State({
            "name":"over",
            "stateGroups":["overStates"],
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_loc1_,
               "destination":null,
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["_ButtonBarMiddleButtonSkin_Rect2"]
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarMiddleButtonSkin_GradientEntry1",
               "name":"alpha",
               "value":0.15
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarMiddleButtonSkin_GradientEntry2",
               "name":"alpha",
               "value":0.05
            })]
         }),new State({
            "name":"down",
            "stateGroups":["downStates"],
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_ButtonBarMiddleButtonSkin_GradientEntry1",
               "name":"alpha",
               "value":0.01
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarMiddleButtonSkin_GradientEntry2",
               "name":"alpha",
               "value":0.01
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarMiddleButtonSkin_GradientEntry3",
               "name":"alpha",
               "value":0.2
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarMiddleButtonSkin_GradientEntry3",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarMiddleButtonSkin_GradientEntry4",
               "name":"alpha",
               "value":0.15
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarMiddleButtonSkin_GradientEntry4",
               "name":"color",
               "value":0
            })]
         }),new State({
            "name":"disabled",
            "stateGroups":["disabledStates"],
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_loc1_,
               "destination":null,
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["_ButtonBarMiddleButtonSkin_Rect2"]
            }),new SetProperty().initializeFromObject({
               "name":"alpha",
               "value":0.5
            })]
         }),new State({
            "name":"upAndSelected",
            "stateGroups":["selectedUpStates","selectedStates"],
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_loc2_,
               "destination":null,
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["_ButtonBarMiddleButtonSkin_Rect2"]
            }),new AddItems().initializeFromObject({
               "itemsFactory":_loc1_,
               "destination":null,
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["_ButtonBarMiddleButtonSkin_Rect2"]
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarMiddleButtonSkin_GradientEntry1",
               "name":"alpha",
               "value":0.05
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarMiddleButtonSkin_GradientEntry2",
               "name":"alpha",
               "value":0.05
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarMiddleButtonSkin_GradientEntry3",
               "name":"alpha",
               "value":0.2
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarMiddleButtonSkin_GradientEntry3",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarMiddleButtonSkin_GradientEntry4",
               "name":"alpha",
               "value":0.15
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarMiddleButtonSkin_GradientEntry4",
               "name":"color",
               "value":0
            })]
         }),new State({
            "name":"overAndSelected",
            "stateGroups":["overStates","selectedStates"],
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_loc2_,
               "destination":null,
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["_ButtonBarMiddleButtonSkin_Rect2"]
            }),new AddItems().initializeFromObject({
               "itemsFactory":_loc1_,
               "destination":null,
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["_ButtonBarMiddleButtonSkin_Rect2"]
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarMiddleButtonSkin_GradientEntry1",
               "name":"alpha",
               "value":0.08
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarMiddleButtonSkin_GradientEntry2",
               "name":"alpha",
               "value":0.08
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarMiddleButtonSkin_GradientEntry3",
               "name":"alpha",
               "value":0.2
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarMiddleButtonSkin_GradientEntry3",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarMiddleButtonSkin_GradientEntry4",
               "name":"alpha",
               "value":0.15
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarMiddleButtonSkin_GradientEntry4",
               "name":"color",
               "value":0
            })]
         }),new State({
            "name":"downAndSelected",
            "stateGroups":["downStates","selectedStates"],
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_loc2_,
               "destination":null,
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["_ButtonBarMiddleButtonSkin_Rect2"]
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarMiddleButtonSkin_GradientEntry1",
               "name":"alpha",
               "value":0.01
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarMiddleButtonSkin_GradientEntry2",
               "name":"alpha",
               "value":0.01
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarMiddleButtonSkin_GradientEntry3",
               "name":"alpha",
               "value":0.2
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarMiddleButtonSkin_GradientEntry3",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarMiddleButtonSkin_GradientEntry4",
               "name":"alpha",
               "value":0.15
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarMiddleButtonSkin_GradientEntry4",
               "name":"color",
               "value":0
            })]
         }),new State({
            "name":"disabledAndSelected",
            "stateGroups":["disabledStates","selectedUpStates","selectedStates"],
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_loc2_,
               "destination":null,
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["_ButtonBarMiddleButtonSkin_Rect2"]
            }),new AddItems().initializeFromObject({
               "itemsFactory":_loc1_,
               "destination":null,
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["_ButtonBarMiddleButtonSkin_Rect2"]
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarMiddleButtonSkin_GradientEntry1",
               "name":"alpha",
               "value":0.05
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarMiddleButtonSkin_GradientEntry2",
               "name":"alpha",
               "value":0.05
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarMiddleButtonSkin_GradientEntry3",
               "name":"alpha",
               "value":0.2
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarMiddleButtonSkin_GradientEntry3",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarMiddleButtonSkin_GradientEntry4",
               "name":"alpha",
               "value":0.15
            }),new SetProperty().initializeFromObject({
               "target":"_ButtonBarMiddleButtonSkin_GradientEntry4",
               "name":"color",
               "value":0
            })]
         })];
      }
      
      override public function set moduleFactory(param1:IFlexModuleFactory) : void
      {
         super.moduleFactory = param1;
         if(this.__moduleFactoryInitialized)
         {
            return;
         }
         this.__moduleFactoryInitialized = true;
      }
      
      override public function initialize() : void
      {
         super.initialize();
      }
      
      override protected function initializationComplete() : void
      {
         useChromeColor = true;
         super.initializationComplete();
      }
      
      override public function get colorizeExclusions() : Array
      {
         return exclusions;
      }
      
      private function _ButtonBarMiddleButtonSkin_Rect1_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 0;
         _loc1_.right = 0;
         _loc1_.top = 0;
         _loc1_.bottom = 0;
         _loc1_.fill = this._ButtonBarMiddleButtonSkin_SolidColor1_c();
         _loc1_.initialized(this,"fillbase");
         this.fillbase = _loc1_;
         BindingManager.executeBindings(this,"fillbase",this.fillbase);
         return _loc1_;
      }
      
      private function _ButtonBarMiddleButtonSkin_SolidColor1_c() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 3355443;
         return _loc1_;
      }
      
      private function _ButtonBarMiddleButtonSkin_Rect2_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 1;
         _loc1_.right = 1;
         _loc1_.top = 1;
         _loc1_.bottom = 1;
         _loc1_.fill = this._ButtonBarMiddleButtonSkin_LinearGradient1_c();
         _loc1_.initialized(this,"_ButtonBarMiddleButtonSkin_Rect2");
         this._ButtonBarMiddleButtonSkin_Rect2 = _loc1_;
         BindingManager.executeBindings(this,"_ButtonBarMiddleButtonSkin_Rect2",this._ButtonBarMiddleButtonSkin_Rect2);
         return _loc1_;
      }
      
      private function _ButtonBarMiddleButtonSkin_LinearGradient1_c() : LinearGradient
      {
         var _loc1_:LinearGradient = new LinearGradient();
         _loc1_.rotation = 90;
         _loc1_.entries = [this._ButtonBarMiddleButtonSkin_GradientEntry1_i(),this._ButtonBarMiddleButtonSkin_GradientEntry2_i()];
         return _loc1_;
      }
      
      private function _ButtonBarMiddleButtonSkin_GradientEntry1_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 16777215;
         _loc1_.alpha = 0.08;
         this._ButtonBarMiddleButtonSkin_GradientEntry1 = _loc1_;
         BindingManager.executeBindings(this,"_ButtonBarMiddleButtonSkin_GradientEntry1",this._ButtonBarMiddleButtonSkin_GradientEntry1);
         return _loc1_;
      }
      
      private function _ButtonBarMiddleButtonSkin_GradientEntry2_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 16777215;
         _loc1_.alpha = 0.03;
         this._ButtonBarMiddleButtonSkin_GradientEntry2 = _loc1_;
         BindingManager.executeBindings(this,"_ButtonBarMiddleButtonSkin_GradientEntry2",this._ButtonBarMiddleButtonSkin_GradientEntry2);
         return _loc1_;
      }
      
      private function _ButtonBarMiddleButtonSkin_Rect3_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 1;
         _loc1_.right = 1;
         _loc1_.top = 1;
         _loc1_.bottom = 1;
         _loc1_.stroke = this._ButtonBarMiddleButtonSkin_LinearGradientStroke1_c();
         _loc1_.initialized(this,"_ButtonBarMiddleButtonSkin_Rect3");
         this._ButtonBarMiddleButtonSkin_Rect3 = _loc1_;
         BindingManager.executeBindings(this,"_ButtonBarMiddleButtonSkin_Rect3",this._ButtonBarMiddleButtonSkin_Rect3);
         return _loc1_;
      }
      
      private function _ButtonBarMiddleButtonSkin_LinearGradientStroke1_c() : LinearGradientStroke
      {
         var _loc1_:LinearGradientStroke = new LinearGradientStroke();
         _loc1_.rotation = 90;
         _loc1_.weight = 1;
         _loc1_.entries = [this._ButtonBarMiddleButtonSkin_GradientEntry3_i(),this._ButtonBarMiddleButtonSkin_GradientEntry4_i()];
         return _loc1_;
      }
      
      private function _ButtonBarMiddleButtonSkin_GradientEntry3_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 16777215;
         _loc1_.alpha = 0.1;
         this._ButtonBarMiddleButtonSkin_GradientEntry3 = _loc1_;
         BindingManager.executeBindings(this,"_ButtonBarMiddleButtonSkin_GradientEntry3",this._ButtonBarMiddleButtonSkin_GradientEntry3);
         return _loc1_;
      }
      
      private function _ButtonBarMiddleButtonSkin_GradientEntry4_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 16777215;
         _loc1_.alpha = 0.03;
         this._ButtonBarMiddleButtonSkin_GradientEntry4 = _loc1_;
         BindingManager.executeBindings(this,"_ButtonBarMiddleButtonSkin_GradientEntry4",this._ButtonBarMiddleButtonSkin_GradientEntry4);
         return _loc1_;
      }
      
      private function _ButtonBarMiddleButtonSkin_Rect4_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 2;
         _loc1_.right = 2;
         _loc1_.top = 2;
         _loc1_.bottom = 2;
         _loc1_.stroke = this._ButtonBarMiddleButtonSkin_LinearGradientStroke2_c();
         _loc1_.initialized(this,"lowlightStroke");
         this.lowlightStroke = _loc1_;
         BindingManager.executeBindings(this,"lowlightStroke",this.lowlightStroke);
         return _loc1_;
      }
      
      private function _ButtonBarMiddleButtonSkin_LinearGradientStroke2_c() : LinearGradientStroke
      {
         var _loc1_:LinearGradientStroke = new LinearGradientStroke();
         _loc1_.rotation = 90;
         _loc1_.weight = 1;
         _loc1_.entries = [this._ButtonBarMiddleButtonSkin_GradientEntry5_c(),this._ButtonBarMiddleButtonSkin_GradientEntry6_c()];
         return _loc1_;
      }
      
      private function _ButtonBarMiddleButtonSkin_GradientEntry5_c() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 0;
         _loc1_.alpha = 0.08;
         return _loc1_;
      }
      
      private function _ButtonBarMiddleButtonSkin_GradientEntry6_c() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 0;
         _loc1_.alpha = 0.03;
         return _loc1_;
      }
      
      private function _ButtonBarMiddleButtonSkin_Rect5_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 0;
         _loc1_.right = 0;
         _loc1_.top = 0;
         _loc1_.bottom = 0;
         _loc1_.stroke = this._ButtonBarMiddleButtonSkin_SolidColorStroke1_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _ButtonBarMiddleButtonSkin_SolidColorStroke1_c() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.color = 0;
         return _loc1_;
      }
      
      private function _ButtonBarMiddleButtonSkin_Label1_i() : Label
      {
         var _loc1_:Label = new Label();
         _loc1_.maxDisplayedLines = 1;
         _loc1_.horizontalCenter = 0;
         _loc1_.verticalCenter = 1;
         _loc1_.left = 10;
         _loc1_.right = 10;
         _loc1_.top = 2;
         _loc1_.bottom = 2;
         _loc1_.setStyle("textAlign","center");
         _loc1_.setStyle("verticalAlign","middle");
         _loc1_.id = "labelDisplay";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         labelDisplay = _loc1_;
         BindingManager.executeBindings(this,"labelDisplay",labelDisplay);
         return _loc1_;
      }
      
      [Bindable(event="propertyChange")]
      public function get _ButtonBarMiddleButtonSkin_GradientEntry1() : GradientEntry
      {
         return this._1669602936_ButtonBarMiddleButtonSkin_GradientEntry1;
      }
      
      public function set _ButtonBarMiddleButtonSkin_GradientEntry1(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._1669602936_ButtonBarMiddleButtonSkin_GradientEntry1;
         if(_loc2_ !== param1)
         {
            this._1669602936_ButtonBarMiddleButtonSkin_GradientEntry1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_ButtonBarMiddleButtonSkin_GradientEntry1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _ButtonBarMiddleButtonSkin_GradientEntry2() : GradientEntry
      {
         return this._1669602935_ButtonBarMiddleButtonSkin_GradientEntry2;
      }
      
      public function set _ButtonBarMiddleButtonSkin_GradientEntry2(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._1669602935_ButtonBarMiddleButtonSkin_GradientEntry2;
         if(_loc2_ !== param1)
         {
            this._1669602935_ButtonBarMiddleButtonSkin_GradientEntry2 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_ButtonBarMiddleButtonSkin_GradientEntry2",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _ButtonBarMiddleButtonSkin_GradientEntry3() : GradientEntry
      {
         return this._1669602934_ButtonBarMiddleButtonSkin_GradientEntry3;
      }
      
      public function set _ButtonBarMiddleButtonSkin_GradientEntry3(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._1669602934_ButtonBarMiddleButtonSkin_GradientEntry3;
         if(_loc2_ !== param1)
         {
            this._1669602934_ButtonBarMiddleButtonSkin_GradientEntry3 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_ButtonBarMiddleButtonSkin_GradientEntry3",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _ButtonBarMiddleButtonSkin_GradientEntry4() : GradientEntry
      {
         return this._1669602933_ButtonBarMiddleButtonSkin_GradientEntry4;
      }
      
      public function set _ButtonBarMiddleButtonSkin_GradientEntry4(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._1669602933_ButtonBarMiddleButtonSkin_GradientEntry4;
         if(_loc2_ !== param1)
         {
            this._1669602933_ButtonBarMiddleButtonSkin_GradientEntry4 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_ButtonBarMiddleButtonSkin_GradientEntry4",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get fillbase() : Rect
      {
         return this._728661292fillbase;
      }
      
      public function set fillbase(param1:Rect) : void
      {
         var _loc2_:Object = this._728661292fillbase;
         if(_loc2_ !== param1)
         {
            this._728661292fillbase = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"fillbase",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get lowlightStroke() : Rect
      {
         return this._1306546406lowlightStroke;
      }
      
      public function set lowlightStroke(param1:Rect) : void
      {
         var _loc2_:Object = this._1306546406lowlightStroke;
         if(_loc2_ !== param1)
         {
            this._1306546406lowlightStroke = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"lowlightStroke",_loc2_,param1));
            }
         }
      }
   }
}

