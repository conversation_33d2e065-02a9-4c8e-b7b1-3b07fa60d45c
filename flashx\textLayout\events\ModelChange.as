package flashx.textLayout.events
{
   public final class ModelChange
   {
      
      public static const ELEMENT_ADDED:String = "elementAdded";
      
      public static const ELEMENT_REMOVAL:String = "elementRemoval";
      
      public static const ELEMENT_MODIFIED:String = "elementModified";
      
      public static const TEXTLAYOUT_FORMAT_CHANGED:String = "formatChanged";
      
      public static const TEXT_INSERTED:String = "textInserted";
      
      public static const TEXT_DELETED:String = "textDeleted";
      
      public static const STYLE_SELECTOR_CHANGED:String = "styleSelectorChanged";
      
      public static const USER_STYLE_CHANGED:String = "userStyleChanged";
      
      public function ModelChange()
      {
         super();
      }
   }
}

