package com.kingnare.skin.spark.HScrollbar
{
   import mx.binding.BindingManager;
   import mx.core.IFlexModuleFactory;
   import mx.core.IStateClient2;
   import mx.events.PropertyChangeEvent;
   import mx.graphics.SolidColor;
   import mx.graphics.SolidColorStroke;
   import mx.states.SetProperty;
   import mx.states.State;
   import spark.components.Button;
   import spark.components.Group;
   import spark.components.supportClasses.Skin;
   import spark.primitives.Rect;
   
   public class HScrollbarThumb extends Skin implements IStateClient2
   {
      
      private var _910777389_HScrollbarThumb_SolidColor1:SolidColor;
      
      private var _910777388_HScrollbarThumb_SolidColor2:SolidColor;
      
      private var _910777387_HScrollbarThumb_SolidColor3:SolidColor;
      
      private var _910777386_HScrollbarThumb_SolidColor4:SolidColor;
      
      private var _766757627_HScrollbarThumb_SolidColorStroke1:SolidColorStroke;
      
      private var _766757628_HScrollbarThumb_SolidColorStroke2:SolidColorStroke;
      
      private var __moduleFactoryInitialized:Boolean = false;
      
      private var _213507019hostComponent:Button;
      
      public function HScrollbarThumb()
      {
         super();
         mx_internal::_document = this;
         this.width = 30;
         this.height = 13;
         this.mxmlContent = [this._HScrollbarThumb_Rect1_c(),this._HScrollbarThumb_Rect2_c(),this._HScrollbarThumb_Rect3_c(),this._HScrollbarThumb_Group1_c()];
         this.currentState = "up";
         states = [new State({
            "name":"up",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_HScrollbarThumb_SolidColor2",
               "name":"alpha",
               "value":0.5
            }),new SetProperty().initializeFromObject({
               "target":"_HScrollbarThumb_SolidColor3",
               "name":"alpha",
               "value":0.5
            }),new SetProperty().initializeFromObject({
               "target":"_HScrollbarThumb_SolidColor4",
               "name":"alpha",
               "value":0.5
            })]
         }),new State({
            "name":"over",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_HScrollbarThumb_SolidColorStroke2",
               "name":"alpha",
               "value":0.23
            }),new SetProperty().initializeFromObject({
               "target":"_HScrollbarThumb_SolidColor1",
               "name":"alpha",
               "value":0.1
            })]
         }),new State({
            "name":"down",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_HScrollbarThumb_SolidColor2",
               "name":"alpha",
               "value":0.5
            }),new SetProperty().initializeFromObject({
               "target":"_HScrollbarThumb_SolidColor3",
               "name":"alpha",
               "value":0.5
            }),new SetProperty().initializeFromObject({
               "target":"_HScrollbarThumb_SolidColor4",
               "name":"alpha",
               "value":0.5
            })]
         }),new State({
            "name":"disabled",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_HScrollbarThumb_SolidColorStroke1",
               "name":"color",
               "value":3355443
            }),new SetProperty().initializeFromObject({
               "target":"_HScrollbarThumb_SolidColor2",
               "name":"alpha",
               "value":0.5
            }),new SetProperty().initializeFromObject({
               "target":"_HScrollbarThumb_SolidColor3",
               "name":"alpha",
               "value":0.5
            }),new SetProperty().initializeFromObject({
               "target":"_HScrollbarThumb_SolidColor4",
               "name":"alpha",
               "value":0.5
            })]
         })];
      }
      
      override public function set moduleFactory(param1:IFlexModuleFactory) : void
      {
         super.moduleFactory = param1;
         if(this.__moduleFactoryInitialized)
         {
            return;
         }
         this.__moduleFactoryInitialized = true;
      }
      
      override public function initialize() : void
      {
         super.initialize();
      }
      
      private function _HScrollbarThumb_Rect1_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.y = 0.5;
         _loc1_.width = 29;
         _loc1_.height = 12;
         _loc1_.left = 0;
         _loc1_.right = 0;
         _loc1_.stroke = this._HScrollbarThumb_SolidColorStroke1_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _HScrollbarThumb_SolidColorStroke1_i() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.alpha = 0.6;
         _loc1_.caps = "square";
         _loc1_.color = 0;
         _loc1_.joints = "miter";
         this._HScrollbarThumb_SolidColorStroke1 = _loc1_;
         BindingManager.executeBindings(this,"_HScrollbarThumb_SolidColorStroke1",this._HScrollbarThumb_SolidColorStroke1);
         return _loc1_;
      }
      
      private function _HScrollbarThumb_Rect2_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.y = 1.5;
         _loc1_.width = 27;
         _loc1_.height = 10;
         _loc1_.left = 1;
         _loc1_.right = 1;
         _loc1_.stroke = this._HScrollbarThumb_SolidColorStroke2_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _HScrollbarThumb_SolidColorStroke2_i() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.alpha = 0.15;
         _loc1_.caps = "square";
         _loc1_.color = 16777215;
         _loc1_.joints = "miter";
         this._HScrollbarThumb_SolidColorStroke2 = _loc1_;
         BindingManager.executeBindings(this,"_HScrollbarThumb_SolidColorStroke2",this._HScrollbarThumb_SolidColorStroke2);
         return _loc1_;
      }
      
      private function _HScrollbarThumb_Rect3_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.y = 2;
         _loc1_.width = 26;
         _loc1_.height = 9;
         _loc1_.left = 2;
         _loc1_.right = 2;
         _loc1_.fill = this._HScrollbarThumb_SolidColor1_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _HScrollbarThumb_SolidColor1_i() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.alpha = 0.05;
         _loc1_.color = 16777215;
         this._HScrollbarThumb_SolidColor1 = _loc1_;
         BindingManager.executeBindings(this,"_HScrollbarThumb_SolidColor1",this._HScrollbarThumb_SolidColor1);
         return _loc1_;
      }
      
      private function _HScrollbarThumb_Group1_c() : Group
      {
         var _loc1_:Group = new Group();
         _loc1_.horizontalCenter = 0.5;
         _loc1_.verticalCenter = 0.5;
         _loc1_.mxmlContent = [this._HScrollbarThumb_Rect4_c(),this._HScrollbarThumb_Rect5_c(),this._HScrollbarThumb_Rect6_c(),this._HScrollbarThumb_Rect7_c(),this._HScrollbarThumb_Rect8_c(),this._HScrollbarThumb_Rect9_c()];
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         return _loc1_;
      }
      
      private function _HScrollbarThumb_Rect4_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.x = 0;
         _loc1_.y = 0;
         _loc1_.width = 1;
         _loc1_.height = 3;
         _loc1_.fill = this._HScrollbarThumb_SolidColor2_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _HScrollbarThumb_SolidColor2_i() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 16777215;
         this._HScrollbarThumb_SolidColor2 = _loc1_;
         BindingManager.executeBindings(this,"_HScrollbarThumb_SolidColor2",this._HScrollbarThumb_SolidColor2);
         return _loc1_;
      }
      
      private function _HScrollbarThumb_Rect5_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.x = 2;
         _loc1_.y = 0;
         _loc1_.width = 1;
         _loc1_.height = 3;
         _loc1_.fill = this._HScrollbarThumb_SolidColor3_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _HScrollbarThumb_SolidColor3_i() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 16777215;
         this._HScrollbarThumb_SolidColor3 = _loc1_;
         BindingManager.executeBindings(this,"_HScrollbarThumb_SolidColor3",this._HScrollbarThumb_SolidColor3);
         return _loc1_;
      }
      
      private function _HScrollbarThumb_Rect6_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.x = 4;
         _loc1_.y = 0;
         _loc1_.width = 1;
         _loc1_.height = 3;
         _loc1_.fill = this._HScrollbarThumb_SolidColor4_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _HScrollbarThumb_SolidColor4_i() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 16777215;
         this._HScrollbarThumb_SolidColor4 = _loc1_;
         BindingManager.executeBindings(this,"_HScrollbarThumb_SolidColor4",this._HScrollbarThumb_SolidColor4);
         return _loc1_;
      }
      
      private function _HScrollbarThumb_Rect7_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.x = 0;
         _loc1_.y = 3;
         _loc1_.width = 1;
         _loc1_.height = 1;
         _loc1_.fill = this._HScrollbarThumb_SolidColor5_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _HScrollbarThumb_SolidColor5_c() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.alpha = 0.3;
         _loc1_.color = 0;
         return _loc1_;
      }
      
      private function _HScrollbarThumb_Rect8_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.x = 2;
         _loc1_.y = 3;
         _loc1_.width = 1;
         _loc1_.height = 1;
         _loc1_.fill = this._HScrollbarThumb_SolidColor6_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _HScrollbarThumb_SolidColor6_c() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.alpha = 0.3;
         _loc1_.color = 0;
         return _loc1_;
      }
      
      private function _HScrollbarThumb_Rect9_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.x = 4;
         _loc1_.y = 3;
         _loc1_.width = 1;
         _loc1_.height = 1;
         _loc1_.fill = this._HScrollbarThumb_SolidColor7_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _HScrollbarThumb_SolidColor7_c() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.alpha = 0.3;
         _loc1_.color = 0;
         return _loc1_;
      }
      
      [Bindable(event="propertyChange")]
      public function get _HScrollbarThumb_SolidColor1() : SolidColor
      {
         return this._910777389_HScrollbarThumb_SolidColor1;
      }
      
      public function set _HScrollbarThumb_SolidColor1(param1:SolidColor) : void
      {
         var _loc2_:Object = this._910777389_HScrollbarThumb_SolidColor1;
         if(_loc2_ !== param1)
         {
            this._910777389_HScrollbarThumb_SolidColor1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_HScrollbarThumb_SolidColor1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _HScrollbarThumb_SolidColor2() : SolidColor
      {
         return this._910777388_HScrollbarThumb_SolidColor2;
      }
      
      public function set _HScrollbarThumb_SolidColor2(param1:SolidColor) : void
      {
         var _loc2_:Object = this._910777388_HScrollbarThumb_SolidColor2;
         if(_loc2_ !== param1)
         {
            this._910777388_HScrollbarThumb_SolidColor2 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_HScrollbarThumb_SolidColor2",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _HScrollbarThumb_SolidColor3() : SolidColor
      {
         return this._910777387_HScrollbarThumb_SolidColor3;
      }
      
      public function set _HScrollbarThumb_SolidColor3(param1:SolidColor) : void
      {
         var _loc2_:Object = this._910777387_HScrollbarThumb_SolidColor3;
         if(_loc2_ !== param1)
         {
            this._910777387_HScrollbarThumb_SolidColor3 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_HScrollbarThumb_SolidColor3",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _HScrollbarThumb_SolidColor4() : SolidColor
      {
         return this._910777386_HScrollbarThumb_SolidColor4;
      }
      
      public function set _HScrollbarThumb_SolidColor4(param1:SolidColor) : void
      {
         var _loc2_:Object = this._910777386_HScrollbarThumb_SolidColor4;
         if(_loc2_ !== param1)
         {
            this._910777386_HScrollbarThumb_SolidColor4 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_HScrollbarThumb_SolidColor4",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _HScrollbarThumb_SolidColorStroke1() : SolidColorStroke
      {
         return this._766757627_HScrollbarThumb_SolidColorStroke1;
      }
      
      public function set _HScrollbarThumb_SolidColorStroke1(param1:SolidColorStroke) : void
      {
         var _loc2_:Object = this._766757627_HScrollbarThumb_SolidColorStroke1;
         if(_loc2_ !== param1)
         {
            this._766757627_HScrollbarThumb_SolidColorStroke1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_HScrollbarThumb_SolidColorStroke1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _HScrollbarThumb_SolidColorStroke2() : SolidColorStroke
      {
         return this._766757628_HScrollbarThumb_SolidColorStroke2;
      }
      
      public function set _HScrollbarThumb_SolidColorStroke2(param1:SolidColorStroke) : void
      {
         var _loc2_:Object = this._766757628_HScrollbarThumb_SolidColorStroke2;
         if(_loc2_ !== param1)
         {
            this._766757628_HScrollbarThumb_SolidColorStroke2 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_HScrollbarThumb_SolidColorStroke2",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get hostComponent() : Button
      {
         return this._213507019hostComponent;
      }
      
      public function set hostComponent(param1:Button) : void
      {
         var _loc2_:Object = this._213507019hostComponent;
         if(_loc2_ !== param1)
         {
            this._213507019hostComponent = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"hostComponent",_loc2_,param1));
            }
         }
      }
   }
}

