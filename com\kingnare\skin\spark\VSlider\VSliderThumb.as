package com.kingnare.skin.spark.VSlider
{
   import mx.binding.BindingManager;
   import mx.core.IFlexModuleFactory;
   import mx.core.IStateClient2;
   import mx.events.PropertyChangeEvent;
   import mx.graphics.GradientEntry;
   import mx.graphics.LinearGradient;
   import mx.graphics.LinearGradientStroke;
   import mx.graphics.SolidColorStroke;
   import mx.states.SetProperty;
   import mx.states.State;
   import spark.components.Button;
   import spark.components.Group;
   import spark.components.supportClasses.Skin;
   import spark.filters.GlowFilter;
   import spark.primitives.Ellipse;
   
   public class VSliderThumb extends Skin implements IStateClient2
   {
      
      private var _701764272_VSliderThumb_GradientEntry1:GradientEntry;
      
      private var _701764273_VSliderThumb_GradientEntry2:GradientEntry;
      
      private var __moduleFactoryInitialized:Boolean = false;
      
      private var _213507019hostComponent:But<PERSON>;
      
      public function VSliderThumb()
      {
         super();
         mx_internal::_document = this;
         this.width = 15;
         this.height = 15;
         this.mxmlContent = [this._VSliderThumb_Group1_c()];
         this.currentState = "up";
         states = [new State({
            "name":"up",
            "overrides":[]
         }),new State({
            "name":"over",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_VSliderThumb_GradientEntry1",
               "name":"color",
               "value":10066329
            })]
         }),new State({
            "name":"down",
            "overrides":[]
         }),new State({
            "name":"disabled",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_VSliderThumb_GradientEntry1",
               "name":"color",
               "value":5592405
            }),new SetProperty().initializeFromObject({
               "target":"_VSliderThumb_GradientEntry2",
               "name":"color",
               "value":5592405
            })]
         })];
      }
      
      override public function set moduleFactory(param1:IFlexModuleFactory) : void
      {
         super.moduleFactory = param1;
         if(this.__moduleFactoryInitialized)
         {
            return;
         }
         this.__moduleFactoryInitialized = true;
      }
      
      override public function initialize() : void
      {
         super.initialize();
      }
      
      private function _VSliderThumb_Group1_c() : Group
      {
         var _loc1_:Group = new Group();
         _loc1_.x = 0;
         _loc1_.y = 0;
         _loc1_.transformX = 1;
         _loc1_.transformY = 1;
         _loc1_.filters = [this._VSliderThumb_GlowFilter1_c()];
         _loc1_.mxmlContent = [this._VSliderThumb_Ellipse1_c(),this._VSliderThumb_Ellipse2_c(),this._VSliderThumb_Ellipse3_c()];
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         return _loc1_;
      }
      
      private function _VSliderThumb_GlowFilter1_c() : GlowFilter
      {
         var _loc1_:GlowFilter = new GlowFilter();
         _loc1_.color = 0;
         _loc1_.blurX = 3;
         _loc1_.blurY = 3;
         _loc1_.alpha = 0.3;
         return _loc1_;
      }
      
      private function _VSliderThumb_Ellipse1_c() : Ellipse
      {
         var _loc1_:Ellipse = new Ellipse();
         _loc1_.x = 0;
         _loc1_.y = 0;
         _loc1_.width = 15;
         _loc1_.height = 15;
         _loc1_.alpha = 1;
         _loc1_.fill = this._VSliderThumb_LinearGradient1_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _VSliderThumb_LinearGradient1_c() : LinearGradient
      {
         var _loc1_:LinearGradient = new LinearGradient();
         _loc1_.rotation = 90;
         _loc1_.entries = [this._VSliderThumb_GradientEntry1_i(),this._VSliderThumb_GradientEntry2_i()];
         return _loc1_;
      }
      
      private function _VSliderThumb_GradientEntry1_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.alpha = 1;
         _loc1_.color = 6710886;
         _loc1_.ratio = 0;
         this._VSliderThumb_GradientEntry1 = _loc1_;
         BindingManager.executeBindings(this,"_VSliderThumb_GradientEntry1",this._VSliderThumb_GradientEntry1);
         return _loc1_;
      }
      
      private function _VSliderThumb_GradientEntry2_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.alpha = 1;
         _loc1_.color = 4144959;
         _loc1_.ratio = 1;
         this._VSliderThumb_GradientEntry2 = _loc1_;
         BindingManager.executeBindings(this,"_VSliderThumb_GradientEntry2",this._VSliderThumb_GradientEntry2);
         return _loc1_;
      }
      
      private function _VSliderThumb_Ellipse2_c() : Ellipse
      {
         var _loc1_:Ellipse = new Ellipse();
         _loc1_.x = 1.5;
         _loc1_.y = 1.5;
         _loc1_.width = 12;
         _loc1_.height = 12;
         _loc1_.alpha = 1;
         _loc1_.stroke = this._VSliderThumb_LinearGradientStroke1_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _VSliderThumb_LinearGradientStroke1_c() : LinearGradientStroke
      {
         var _loc1_:LinearGradientStroke = new LinearGradientStroke();
         _loc1_.caps = "none";
         _loc1_.joints = "miter";
         _loc1_.miterLimit = 4;
         _loc1_.rotation = 90;
         _loc1_.weight = 1;
         _loc1_.entries = [this._VSliderThumb_GradientEntry3_c(),this._VSliderThumb_GradientEntry4_c()];
         return _loc1_;
      }
      
      private function _VSliderThumb_GradientEntry3_c() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.alpha = 0.2;
         _loc1_.color = 16777215;
         _loc1_.ratio = 0;
         return _loc1_;
      }
      
      private function _VSliderThumb_GradientEntry4_c() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.alpha = 0;
         _loc1_.color = 16777215;
         _loc1_.ratio = 1;
         return _loc1_;
      }
      
      private function _VSliderThumb_Ellipse3_c() : Ellipse
      {
         var _loc1_:Ellipse = new Ellipse();
         _loc1_.x = 0.5;
         _loc1_.y = 0.5;
         _loc1_.width = 14;
         _loc1_.height = 14;
         _loc1_.stroke = this._VSliderThumb_SolidColorStroke1_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _VSliderThumb_SolidColorStroke1_c() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.alpha = 1;
         _loc1_.color = 0;
         return _loc1_;
      }
      
      [Bindable(event="propertyChange")]
      public function get _VSliderThumb_GradientEntry1() : GradientEntry
      {
         return this._701764272_VSliderThumb_GradientEntry1;
      }
      
      public function set _VSliderThumb_GradientEntry1(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._701764272_VSliderThumb_GradientEntry1;
         if(_loc2_ !== param1)
         {
            this._701764272_VSliderThumb_GradientEntry1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_VSliderThumb_GradientEntry1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _VSliderThumb_GradientEntry2() : GradientEntry
      {
         return this._701764273_VSliderThumb_GradientEntry2;
      }
      
      public function set _VSliderThumb_GradientEntry2(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._701764273_VSliderThumb_GradientEntry2;
         if(_loc2_ !== param1)
         {
            this._701764273_VSliderThumb_GradientEntry2 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_VSliderThumb_GradientEntry2",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get hostComponent() : Button
      {
         return this._213507019hostComponent;
      }
      
      public function set hostComponent(param1:Button) : void
      {
         var _loc2_:Object = this._213507019hostComponent;
         if(_loc2_ !== param1)
         {
            this._213507019hostComponent = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"hostComponent",_loc2_,param1));
            }
         }
      }
   }
}

