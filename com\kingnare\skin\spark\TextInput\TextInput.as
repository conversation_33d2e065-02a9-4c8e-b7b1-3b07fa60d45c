package com.kingnare.skin.spark.TextInput
{
   import flash.events.FocusEvent;
   import mx.binding.BindingManager;
   import mx.core.IFlexModuleFactory;
   import mx.core.IStateClient2;
   import mx.events.PropertyChangeEvent;
   import mx.graphics.GradientEntry;
   import mx.graphics.LinearGradient;
   import mx.graphics.SolidColor;
   import mx.graphics.SolidColorStroke;
   import mx.states.SetProperty;
   import mx.states.State;
   import spark.components.RichEditableText;
   import spark.components.TextInput;
   import spark.components.supportClasses.Skin;
   import spark.primitives.Rect;
   
   public class TextInput extends Skin implements IStateClient2
   {
      
      private var _78868433_TextInput_Rect4:Rect;
      
      private var _852341288_TextInput_SolidColor1:SolidColor;
      
      private var _852341287_TextInput_SolidColor2:SolidColor;
      
      private var _852341286_TextInput_SolidColor3:SolidColor;
      
      private var _852341285_TextInput_SolidColor4:SolidColor;
      
      private var _1422335297_TextInput_SolidColorStroke2:SolidColorStroke;
      
      private var _831827669textDisplay:RichEditableText;
      
      private var _1408915019whiteBorder:SolidColorStroke;
      
      private var __moduleFactoryInitialized:Boolean = false;
      
      private var _213507019hostComponent:spark.components.TextInput;
      
      public function TextInput()
      {
         super();
         mx_internal::_document = this;
         this.mxmlContent = [this._TextInput_Rect1_c(),this._TextInput_Rect2_c(),this._TextInput_Rect3_c(),this._TextInput_Rect4_i(),this._TextInput_Rect5_c(),this._TextInput_Rect6_c(),this._TextInput_Rect7_c(),this._TextInput_Rect8_c(),this._TextInput_RichEditableText1_i()];
         this.currentState = "normal";
         states = [new State({
            "name":"normal",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"whiteBorder",
               "name":"alpha",
               "value":0.1
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_SolidColor1",
               "name":"alpha",
               "value":0.1
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_SolidColor1",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_SolidColor2",
               "name":"alpha",
               "value":0.15
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_SolidColor2",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_Rect4",
               "name":"top",
               "value":3
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_SolidColor3",
               "name":"alpha",
               "value":0.08
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_SolidColor3",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_SolidColor4",
               "name":"alpha",
               "value":0.3
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_SolidColorStroke2",
               "name":"color",
               "value":0
            })]
         }),new State({
            "name":"disabled",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"whiteBorder",
               "name":"alpha",
               "value":0.08
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_SolidColor1",
               "name":"alpha",
               "value":0.03
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_SolidColor1",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_SolidColor2",
               "name":"alpha",
               "value":0.01
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_SolidColor2",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_Rect4",
               "name":"top",
               "value":2
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_SolidColor3",
               "name":"alpha",
               "value":0.03
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_SolidColor3",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_SolidColor4",
               "name":"alpha",
               "value":0.2
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_SolidColorStroke2",
               "name":"color",
               "value":1118481
            })]
         }),new State({
            "name":"normalWithPrompt",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"whiteBorder",
               "name":"alpha",
               "value":0.1
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_SolidColor1",
               "name":"alpha",
               "value":0.1
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_SolidColor1",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_SolidColor2",
               "name":"alpha",
               "value":0.15
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_SolidColor2",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_Rect4",
               "name":"top",
               "value":3
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_SolidColor3",
               "name":"alpha",
               "value":0.08
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_SolidColor3",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_SolidColor4",
               "name":"alpha",
               "value":0.3
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_SolidColorStroke2",
               "name":"color",
               "value":0
            })]
         }),new State({
            "name":"disabledWithPrompt",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"whiteBorder",
               "name":"alpha",
               "value":0.08
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_SolidColor1",
               "name":"alpha",
               "value":0.03
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_SolidColor1",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_SolidColor2",
               "name":"alpha",
               "value":0.01
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_SolidColor2",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_Rect4",
               "name":"top",
               "value":2
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_SolidColor3",
               "name":"alpha",
               "value":0.03
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_SolidColor3",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_SolidColor4",
               "name":"alpha",
               "value":0.2
            }),new SetProperty().initializeFromObject({
               "target":"_TextInput_SolidColorStroke2",
               "name":"color",
               "value":1118481
            })]
         })];
      }
      
      override public function set moduleFactory(param1:IFlexModuleFactory) : void
      {
         super.moduleFactory = param1;
         if(this.__moduleFactoryInitialized)
         {
            return;
         }
         this.__moduleFactoryInitialized = true;
      }
      
      override public function initialize() : void
      {
         super.initialize();
      }
      
      protected function txtFocusInHandler(param1:FocusEvent) : void
      {
         this.whiteBorder.alpha = 0.15;
      }
      
      protected function txtFocusOutHandler(param1:FocusEvent) : void
      {
         this.whiteBorder.alpha = 0.1;
      }
      
      private function _TextInput_Rect1_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 0;
         _loc1_.right = 0;
         _loc1_.top = 0;
         _loc1_.bottom = 0;
         _loc1_.stroke = this._TextInput_SolidColorStroke1_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _TextInput_SolidColorStroke1_i() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.caps = "square";
         _loc1_.color = 16777215;
         _loc1_.joints = "miter";
         this.whiteBorder = _loc1_;
         BindingManager.executeBindings(this,"whiteBorder",this.whiteBorder);
         return _loc1_;
      }
      
      private function _TextInput_Rect2_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 2;
         _loc1_.right = 2;
         _loc1_.bottom = 4;
         _loc1_.height = 3;
         _loc1_.fill = this._TextInput_SolidColor1_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _TextInput_SolidColor1_i() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         this._TextInput_SolidColor1 = _loc1_;
         BindingManager.executeBindings(this,"_TextInput_SolidColor1",this._TextInput_SolidColor1);
         return _loc1_;
      }
      
      private function _TextInput_Rect3_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 2;
         _loc1_.right = 2;
         _loc1_.bottom = 2;
         _loc1_.height = 2;
         _loc1_.fill = this._TextInput_SolidColor2_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _TextInput_SolidColor2_i() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         this._TextInput_SolidColor2 = _loc1_;
         BindingManager.executeBindings(this,"_TextInput_SolidColor2",this._TextInput_SolidColor2);
         return _loc1_;
      }
      
      private function _TextInput_Rect4_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 2;
         _loc1_.right = 2;
         _loc1_.bottom = 7;
         _loc1_.fill = this._TextInput_SolidColor3_i();
         _loc1_.initialized(this,"_TextInput_Rect4");
         this._TextInput_Rect4 = _loc1_;
         BindingManager.executeBindings(this,"_TextInput_Rect4",this._TextInput_Rect4);
         return _loc1_;
      }
      
      private function _TextInput_SolidColor3_i() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         this._TextInput_SolidColor3 = _loc1_;
         BindingManager.executeBindings(this,"_TextInput_SolidColor3",this._TextInput_SolidColor3);
         return _loc1_;
      }
      
      private function _TextInput_Rect5_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 2;
         _loc1_.right = 2;
         _loc1_.top = 2;
         _loc1_.height = 1;
         _loc1_.alpha = 1;
         _loc1_.fill = this._TextInput_SolidColor4_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _TextInput_SolidColor4_i() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 0;
         this._TextInput_SolidColor4 = _loc1_;
         BindingManager.executeBindings(this,"_TextInput_SolidColor4",this._TextInput_SolidColor4);
         return _loc1_;
      }
      
      private function _TextInput_Rect6_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 1;
         _loc1_.right = 1;
         _loc1_.top = 1;
         _loc1_.bottom = 1;
         _loc1_.stroke = this._TextInput_SolidColorStroke2_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _TextInput_SolidColorStroke2_i() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.alpha = 1;
         _loc1_.caps = "square";
         _loc1_.joints = "miter";
         _loc1_.miterLimit = 4;
         _loc1_.weight = 1;
         this._TextInput_SolidColorStroke2 = _loc1_;
         BindingManager.executeBindings(this,"_TextInput_SolidColorStroke2",this._TextInput_SolidColorStroke2);
         return _loc1_;
      }
      
      private function _TextInput_Rect7_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 2;
         _loc1_.top = 3;
         _loc1_.bottom = 1;
         _loc1_.width = 1;
         _loc1_.fill = this._TextInput_LinearGradient1_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _TextInput_LinearGradient1_c() : LinearGradient
      {
         var _loc1_:LinearGradient = new LinearGradient();
         _loc1_.rotation = 90;
         _loc1_.entries = [this._TextInput_GradientEntry1_c(),this._TextInput_GradientEntry2_c()];
         return _loc1_;
      }
      
      private function _TextInput_GradientEntry1_c() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.alpha = 0.2;
         _loc1_.color = 0;
         _loc1_.ratio = 0;
         return _loc1_;
      }
      
      private function _TextInput_GradientEntry2_c() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.alpha = 0;
         _loc1_.color = 0;
         _loc1_.ratio = 1;
         return _loc1_;
      }
      
      private function _TextInput_Rect8_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.right = 2;
         _loc1_.top = 3;
         _loc1_.bottom = 1;
         _loc1_.width = 1;
         _loc1_.fill = this._TextInput_LinearGradient2_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _TextInput_LinearGradient2_c() : LinearGradient
      {
         var _loc1_:LinearGradient = new LinearGradient();
         _loc1_.rotation = 90;
         _loc1_.entries = [this._TextInput_GradientEntry3_c(),this._TextInput_GradientEntry4_c()];
         return _loc1_;
      }
      
      private function _TextInput_GradientEntry3_c() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.alpha = 0.2;
         _loc1_.color = 0;
         _loc1_.ratio = 0;
         return _loc1_;
      }
      
      private function _TextInput_GradientEntry4_c() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.alpha = 0;
         _loc1_.color = 0;
         _loc1_.ratio = 1;
         return _loc1_;
      }
      
      private function _TextInput_RichEditableText1_i() : RichEditableText
      {
         var _loc1_:RichEditableText = new RichEditableText();
         _loc1_.left = 2;
         _loc1_.right = 2;
         _loc1_.top = 2;
         _loc1_.bottom = 2;
         _loc1_.lineBreak = "explicit";
         _loc1_.widthInChars = 10;
         _loc1_.setStyle("paddingBottom",3);
         _loc1_.setStyle("paddingLeft",3);
         _loc1_.setStyle("paddingRight",3);
         _loc1_.setStyle("paddingTop",5);
         _loc1_.setStyle("verticalAlign","middle");
         _loc1_.addEventListener("focusIn",this.__textDisplay_focusIn);
         _loc1_.addEventListener("focusOut",this.__textDisplay_focusOut);
         _loc1_.id = "textDisplay";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         this.textDisplay = _loc1_;
         BindingManager.executeBindings(this,"textDisplay",this.textDisplay);
         return _loc1_;
      }
      
      public function __textDisplay_focusIn(param1:FocusEvent) : void
      {
         this.txtFocusInHandler(param1);
      }
      
      public function __textDisplay_focusOut(param1:FocusEvent) : void
      {
         this.txtFocusOutHandler(param1);
      }
      
      [Bindable(event="propertyChange")]
      public function get _TextInput_Rect4() : Rect
      {
         return this._78868433_TextInput_Rect4;
      }
      
      public function set _TextInput_Rect4(param1:Rect) : void
      {
         var _loc2_:Object = this._78868433_TextInput_Rect4;
         if(_loc2_ !== param1)
         {
            this._78868433_TextInput_Rect4 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_TextInput_Rect4",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _TextInput_SolidColor1() : SolidColor
      {
         return this._852341288_TextInput_SolidColor1;
      }
      
      public function set _TextInput_SolidColor1(param1:SolidColor) : void
      {
         var _loc2_:Object = this._852341288_TextInput_SolidColor1;
         if(_loc2_ !== param1)
         {
            this._852341288_TextInput_SolidColor1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_TextInput_SolidColor1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _TextInput_SolidColor2() : SolidColor
      {
         return this._852341287_TextInput_SolidColor2;
      }
      
      public function set _TextInput_SolidColor2(param1:SolidColor) : void
      {
         var _loc2_:Object = this._852341287_TextInput_SolidColor2;
         if(_loc2_ !== param1)
         {
            this._852341287_TextInput_SolidColor2 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_TextInput_SolidColor2",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _TextInput_SolidColor3() : SolidColor
      {
         return this._852341286_TextInput_SolidColor3;
      }
      
      public function set _TextInput_SolidColor3(param1:SolidColor) : void
      {
         var _loc2_:Object = this._852341286_TextInput_SolidColor3;
         if(_loc2_ !== param1)
         {
            this._852341286_TextInput_SolidColor3 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_TextInput_SolidColor3",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _TextInput_SolidColor4() : SolidColor
      {
         return this._852341285_TextInput_SolidColor4;
      }
      
      public function set _TextInput_SolidColor4(param1:SolidColor) : void
      {
         var _loc2_:Object = this._852341285_TextInput_SolidColor4;
         if(_loc2_ !== param1)
         {
            this._852341285_TextInput_SolidColor4 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_TextInput_SolidColor4",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _TextInput_SolidColorStroke2() : SolidColorStroke
      {
         return this._1422335297_TextInput_SolidColorStroke2;
      }
      
      public function set _TextInput_SolidColorStroke2(param1:SolidColorStroke) : void
      {
         var _loc2_:Object = this._1422335297_TextInput_SolidColorStroke2;
         if(_loc2_ !== param1)
         {
            this._1422335297_TextInput_SolidColorStroke2 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_TextInput_SolidColorStroke2",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get textDisplay() : RichEditableText
      {
         return this._831827669textDisplay;
      }
      
      public function set textDisplay(param1:RichEditableText) : void
      {
         var _loc2_:Object = this._831827669textDisplay;
         if(_loc2_ !== param1)
         {
            this._831827669textDisplay = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"textDisplay",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get whiteBorder() : SolidColorStroke
      {
         return this._1408915019whiteBorder;
      }
      
      public function set whiteBorder(param1:SolidColorStroke) : void
      {
         var _loc2_:Object = this._1408915019whiteBorder;
         if(_loc2_ !== param1)
         {
            this._1408915019whiteBorder = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"whiteBorder",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get hostComponent() : spark.components.TextInput
      {
         return this._213507019hostComponent;
      }
      
      public function set hostComponent(param1:spark.components.TextInput) : void
      {
         var _loc2_:Object = this._213507019hostComponent;
         if(_loc2_ !== param1)
         {
            this._213507019hostComponent = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"hostComponent",_loc2_,param1));
            }
         }
      }
   }
}

