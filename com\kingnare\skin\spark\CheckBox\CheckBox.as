package com.kingnare.skin.spark.CheckBox
{
   import mx.binding.BindingManager;
   import mx.core.IFlexModuleFactory;
   import mx.core.IStateClient2;
   import mx.events.PropertyChangeEvent;
   import mx.graphics.SolidColor;
   import mx.graphics.SolidColorStroke;
   import mx.states.SetProperty;
   import mx.states.State;
   import spark.components.CheckBox;
   import spark.components.Group;
   import spark.components.HGroup;
   import spark.components.RichText;
   import spark.components.supportClasses.Skin;
   import spark.primitives.Path;
   import spark.primitives.Rect;
   
   public class CheckBox extends Skin implements IStateClient2
   {
      
      private var _909055185_CheckBox_Path1:Path;
      
      private var _907104944_CheckBox_Rect1:Rect;
      
      private var _527106045_CheckBox_SolidColor2:SolidColor;
      
      private var _1184053038labelDisplay:RichText;
      
      private var __moduleFactoryInitialized:Boolean = false;
      
      private var _213507019hostComponent:spark.components.CheckBox;
      
      public function CheckBox()
      {
         super();
         mx_internal::_document = this;
         this.mxmlContent = [this._CheckBox_HGroup1_c()];
         this.currentState = "up";
         states = [new State({
            "name":"up",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_CheckBox_Path1",
               "name":"visible",
               "value":false
            }),new SetProperty().initializeFromObject({
               "target":"labelDisplay",
               "name":"verticalCenter",
               "value":0.5
            })]
         }),new State({
            "name":"over",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_CheckBox_Rect1",
               "name":"alpha",
               "value":0.3
            }),new SetProperty().initializeFromObject({
               "target":"_CheckBox_Rect1",
               "name":"stroke",
               "value":this._CheckBox_SolidColorStroke2_c()
            }),new SetProperty().initializeFromObject({
               "target":"_CheckBox_Path1",
               "name":"visible",
               "value":false
            }),new SetProperty().initializeFromObject({
               "target":"labelDisplay",
               "name":"verticalCenter",
               "value":0.5
            })]
         }),new State({
            "name":"down",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_CheckBox_Rect1",
               "name":"alpha",
               "value":0.3
            }),new SetProperty().initializeFromObject({
               "target":"_CheckBox_Rect1",
               "name":"stroke",
               "value":this._CheckBox_SolidColorStroke3_c()
            }),new SetProperty().initializeFromObject({
               "target":"_CheckBox_Path1",
               "name":"visible",
               "value":false
            }),new SetProperty().initializeFromObject({
               "target":"labelDisplay",
               "name":"verticalCenter",
               "value":0.5
            })]
         }),new State({
            "name":"disabled",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_CheckBox_Path1",
               "name":"visible",
               "value":false
            }),new SetProperty().initializeFromObject({
               "target":"labelDisplay",
               "name":"alpha",
               "value":0.5
            }),new SetProperty().initializeFromObject({
               "target":"labelDisplay",
               "name":"verticalCenter",
               "value":0.5
            })]
         }),new State({
            "name":"upAndSelected",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"labelDisplay",
               "name":"verticalCenter",
               "value":0.5
            })]
         }),new State({
            "name":"overAndSelected",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_CheckBox_Rect1",
               "name":"alpha",
               "value":0.3
            }),new SetProperty().initializeFromObject({
               "target":"_CheckBox_Rect1",
               "name":"stroke",
               "value":this._CheckBox_SolidColorStroke4_c()
            }),new SetProperty().initializeFromObject({
               "target":"_CheckBox_SolidColor2",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"labelDisplay",
               "name":"verticalCenter",
               "value":0.5
            })]
         }),new State({
            "name":"downAndSelected",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_CheckBox_Rect1",
               "name":"alpha",
               "value":0.3
            }),new SetProperty().initializeFromObject({
               "target":"_CheckBox_Rect1",
               "name":"stroke",
               "value":this._CheckBox_SolidColorStroke5_c()
            }),new SetProperty().initializeFromObject({
               "target":"labelDisplay",
               "name":"verticalCenter",
               "value":0.5
            })]
         }),new State({
            "name":"disabledAndSelected",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_CheckBox_SolidColor2",
               "name":"color",
               "value":4671303
            }),new SetProperty().initializeFromObject({
               "target":"labelDisplay",
               "name":"bottom",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"labelDisplay",
               "name":"width",
               "value":55
            }),new SetProperty().initializeFromObject({
               "target":"labelDisplay",
               "name":"alpha",
               "value":0.5
            })]
         })];
      }
      
      override public function set moduleFactory(param1:IFlexModuleFactory) : void
      {
         super.moduleFactory = param1;
         if(this.__moduleFactoryInitialized)
         {
            return;
         }
         this.__moduleFactoryInitialized = true;
      }
      
      override public function initialize() : void
      {
         super.initialize();
      }
      
      private function _CheckBox_HGroup1_c() : HGroup
      {
         var _loc1_:HGroup = new HGroup();
         _loc1_.percentWidth = 100;
         _loc1_.percentHeight = 100;
         _loc1_.verticalAlign = "middle";
         _loc1_.horizontalAlign = "center";
         _loc1_.gap = 5;
         _loc1_.mxmlContent = [this._CheckBox_Group1_c(),this._CheckBox_RichText1_i()];
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         return _loc1_;
      }
      
      private function _CheckBox_Group1_c() : Group
      {
         var _loc1_:Group = new Group();
         _loc1_.x = 0;
         _loc1_.verticalCenter = 0;
         _loc1_.mxmlContent = [this._CheckBox_Rect1_i(),this._CheckBox_Rect2_c(),this._CheckBox_Rect3_c(),this._CheckBox_Rect4_c(),this._CheckBox_Path1_i()];
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         return _loc1_;
      }
      
      private function _CheckBox_Rect1_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.x = 0.5;
         _loc1_.y = 0.5;
         _loc1_.width = 15;
         _loc1_.height = 15;
         _loc1_.stroke = this._CheckBox_SolidColorStroke1_c();
         _loc1_.fill = this._CheckBox_SolidColor1_c();
         _loc1_.initialized(this,"_CheckBox_Rect1");
         this._CheckBox_Rect1 = _loc1_;
         BindingManager.executeBindings(this,"_CheckBox_Rect1",this._CheckBox_Rect1);
         return _loc1_;
      }
      
      private function _CheckBox_SolidColorStroke1_c() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.alpha = 0.1;
         _loc1_.caps = "none";
         _loc1_.color = 13027014;
         _loc1_.joints = "miter";
         _loc1_.miterLimit = 4;
         _loc1_.weight = 1;
         return _loc1_;
      }
      
      private function _CheckBox_SolidColor1_c() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 16777215;
         _loc1_.alpha = 0;
         return _loc1_;
      }
      
      private function _CheckBox_Rect2_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.x = 1.5;
         _loc1_.y = 1.5;
         _loc1_.width = 13;
         _loc1_.height = 13;
         _loc1_.stroke = this._CheckBox_SolidColorStroke6_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _CheckBox_SolidColorStroke6_c() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.caps = "none";
         _loc1_.color = 1776411;
         _loc1_.joints = "miter";
         _loc1_.miterLimit = 4;
         _loc1_.weight = 1;
         return _loc1_;
      }
      
      private function _CheckBox_Rect3_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.x = 2.5;
         _loc1_.y = 2.5;
         _loc1_.width = 11;
         _loc1_.height = 11;
         _loc1_.stroke = this._CheckBox_SolidColorStroke7_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _CheckBox_SolidColorStroke7_c() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.caps = "none";
         _loc1_.color = 2171169;
         _loc1_.joints = "miter";
         _loc1_.miterLimit = 4;
         _loc1_.weight = 1;
         return _loc1_;
      }
      
      private function _CheckBox_Rect4_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.x = 3.5;
         _loc1_.y = 3.5;
         _loc1_.width = 9;
         _loc1_.height = 9;
         _loc1_.stroke = this._CheckBox_SolidColorStroke8_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _CheckBox_SolidColorStroke8_c() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.caps = "none";
         _loc1_.color = 2829099;
         _loc1_.joints = "miter";
         _loc1_.miterLimit = 4;
         _loc1_.weight = 1;
         return _loc1_;
      }
      
      private function _CheckBox_Path1_i() : Path
      {
         var _loc1_:Path = new Path();
         _loc1_.data = "M 3 6 L 4 6 L 4 5 L 5 5 L 5 6 L 6 6 L 6 7 L 7 7 L 7 6 L 8 6 L 8 5 L 9 5 L 9 4 L 10 4 L 10 3 L 11 3 L 11 4 L 12 4 L 12 5 L 11 5 L 11 6 L 10 6 L 10 7 L 9 7 L 9 8 L 8 8 L 8 9 L 7 9 L 7 10 L 6 10 L 6 9 L 5 9 L 5 8 L 4 8 L 4 7 L 3 7 Z";
         _loc1_.scaleX = 1.1;
         _loc1_.scaleY = 1.1;
         _loc1_.fill = this._CheckBox_SolidColor2_i();
         _loc1_.initialized(this,"_CheckBox_Path1");
         this._CheckBox_Path1 = _loc1_;
         BindingManager.executeBindings(this,"_CheckBox_Path1",this._CheckBox_Path1);
         return _loc1_;
      }
      
      private function _CheckBox_SolidColor2_i() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 13421772;
         this._CheckBox_SolidColor2 = _loc1_;
         BindingManager.executeBindings(this,"_CheckBox_SolidColor2",this._CheckBox_SolidColor2);
         return _loc1_;
      }
      
      private function _CheckBox_RichText1_i() : RichText
      {
         var _loc1_:RichText = new RichText();
         _loc1_.x = 18;
         _loc1_.setStyle("textAlign","start");
         _loc1_.setStyle("verticalAlign","middle");
         _loc1_.id = "labelDisplay";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         this.labelDisplay = _loc1_;
         BindingManager.executeBindings(this,"labelDisplay",this.labelDisplay);
         return _loc1_;
      }
      
      private function _CheckBox_SolidColorStroke2_c() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.caps = "none";
         _loc1_.color = 13027014;
         _loc1_.joints = "miter";
         _loc1_.miterLimit = 4;
         _loc1_.weight = 1;
         return _loc1_;
      }
      
      private function _CheckBox_SolidColorStroke3_c() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.caps = "none";
         _loc1_.color = 13027014;
         _loc1_.joints = "miter";
         _loc1_.miterLimit = 4;
         _loc1_.weight = 1;
         return _loc1_;
      }
      
      private function _CheckBox_SolidColorStroke4_c() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.caps = "none";
         _loc1_.color = 13027014;
         _loc1_.joints = "miter";
         _loc1_.miterLimit = 4;
         _loc1_.weight = 1;
         return _loc1_;
      }
      
      private function _CheckBox_SolidColorStroke5_c() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.caps = "none";
         _loc1_.color = 13027014;
         _loc1_.joints = "miter";
         _loc1_.miterLimit = 4;
         _loc1_.weight = 1;
         return _loc1_;
      }
      
      [Bindable(event="propertyChange")]
      public function get _CheckBox_Path1() : Path
      {
         return this._909055185_CheckBox_Path1;
      }
      
      public function set _CheckBox_Path1(param1:Path) : void
      {
         var _loc2_:Object = this._909055185_CheckBox_Path1;
         if(_loc2_ !== param1)
         {
            this._909055185_CheckBox_Path1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_CheckBox_Path1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _CheckBox_Rect1() : Rect
      {
         return this._907104944_CheckBox_Rect1;
      }
      
      public function set _CheckBox_Rect1(param1:Rect) : void
      {
         var _loc2_:Object = this._907104944_CheckBox_Rect1;
         if(_loc2_ !== param1)
         {
            this._907104944_CheckBox_Rect1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_CheckBox_Rect1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _CheckBox_SolidColor2() : SolidColor
      {
         return this._527106045_CheckBox_SolidColor2;
      }
      
      public function set _CheckBox_SolidColor2(param1:SolidColor) : void
      {
         var _loc2_:Object = this._527106045_CheckBox_SolidColor2;
         if(_loc2_ !== param1)
         {
            this._527106045_CheckBox_SolidColor2 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_CheckBox_SolidColor2",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get labelDisplay() : RichText
      {
         return this._1184053038labelDisplay;
      }
      
      public function set labelDisplay(param1:RichText) : void
      {
         var _loc2_:Object = this._1184053038labelDisplay;
         if(_loc2_ !== param1)
         {
            this._1184053038labelDisplay = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"labelDisplay",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get hostComponent() : spark.components.CheckBox
      {
         return this._213507019hostComponent;
      }
      
      public function set hostComponent(param1:spark.components.CheckBox) : void
      {
         var _loc2_:Object = this._213507019hostComponent;
         if(_loc2_ !== param1)
         {
            this._213507019hostComponent = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"hostComponent",_loc2_,param1));
            }
         }
      }
   }
}

