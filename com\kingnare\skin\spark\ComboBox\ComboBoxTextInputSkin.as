package com.kingnare.skin.spark.ComboBox
{
   import mx.binding.BindingManager;
   import mx.core.DeferredInstanceFromFunction;
   import mx.core.IFlexModuleFactory;
   import mx.core.IStateClient2;
   import mx.events.PropertyChangeEvent;
   import mx.graphics.GradientEntry;
   import mx.graphics.LinearGradient;
   import mx.graphics.SolidColor;
   import mx.graphics.SolidColorStroke;
   import mx.states.AddItems;
   import mx.states.SetProperty;
   import mx.states.State;
   import spark.components.Label;
   import spark.components.RichEditableText;
   import spark.components.TextInput;
   import spark.primitives.Rect;
   import spark.skins.SparkSkin;
   
   public class ComboBoxTextInputSkin extends SparkSkin implements IStateClient2
   {
      
      private static const exclusions:Array = ["background","textDisplay","promptDisplay"];
      
      private static const contentFill:Array = [];
      
      private static const focusExclusions:Array = ["textDisplay"];
      
      private var _185313135_ComboBoxTextInputSkin_Rect4:Rect;
      
      private var _1164774120_ComboBoxTextInputSkin_SolidColor1:SolidColor;
      
      private var _1164774119_ComboBoxTextInputSkin_SolidColor2:SolidColor;
      
      private var _1164774117_ComboBoxTextInputSkin_SolidColor4:SolidColor;
      
      private var _610096256_ComboBoxTextInputSkin_SolidColorStroke1:SolidColorStroke;
      
      private var _610096257_ComboBoxTextInputSkin_SolidColorStroke2:SolidColorStroke;
      
      private var _1391998104bgFill:SolidColor;
      
      private var _263438014promptDisplay:Label;
      
      private var _831827669textDisplay:RichEditableText;
      
      private var __moduleFactoryInitialized:Boolean = false;
      
      private var paddingChanged:Boolean;
      
      private var cornerRadius:Number = 0;
      
      private var _213507019hostComponent:TextInput;
      
      public function ComboBoxTextInputSkin()
      {
         super();
         mx_internal::_document = this;
         this.blendMode = "normal";
         this.mxmlContent = [this._ComboBoxTextInputSkin_Rect1_c(),this._ComboBoxTextInputSkin_Rect2_c(),this._ComboBoxTextInputSkin_Rect3_c(),this._ComboBoxTextInputSkin_Rect4_i(),this._ComboBoxTextInputSkin_Rect5_c(),this._ComboBoxTextInputSkin_Rect6_c(),this._ComboBoxTextInputSkin_Rect7_c(),this._ComboBoxTextInputSkin_Rect8_c(),this._ComboBoxTextInputSkin_RichEditableText1_i()];
         this.currentState = "normal";
         var _loc1_:DeferredInstanceFromFunction = new DeferredInstanceFromFunction(this._ComboBoxTextInputSkin_Label1_i);
         states = [new State({
            "name":"normal",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_ComboBoxTextInputSkin_SolidColorStroke1",
               "name":"alpha",
               "value":0.1
            }),new SetProperty().initializeFromObject({
               "target":"_ComboBoxTextInputSkin_SolidColor1",
               "name":"alpha",
               "value":0.1
            }),new SetProperty().initializeFromObject({
               "target":"_ComboBoxTextInputSkin_SolidColor1",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_ComboBoxTextInputSkin_SolidColor2",
               "name":"alpha",
               "value":0.15
            }),new SetProperty().initializeFromObject({
               "target":"_ComboBoxTextInputSkin_SolidColor2",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_ComboBoxTextInputSkin_Rect4",
               "name":"top",
               "value":3
            }),new SetProperty().initializeFromObject({
               "target":"bgFill",
               "name":"alpha",
               "value":0.08
            }),new SetProperty().initializeFromObject({
               "target":"bgFill",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_ComboBoxTextInputSkin_SolidColor4",
               "name":"alpha",
               "value":0.3
            }),new SetProperty().initializeFromObject({
               "target":"_ComboBoxTextInputSkin_SolidColorStroke2",
               "name":"color",
               "value":0
            })]
         }),new State({
            "name":"disabled",
            "stateGroups":["disabledStates"],
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_ComboBoxTextInputSkin_SolidColorStroke1",
               "name":"alpha",
               "value":0.08
            }),new SetProperty().initializeFromObject({
               "target":"_ComboBoxTextInputSkin_SolidColor1",
               "name":"alpha",
               "value":0.03
            }),new SetProperty().initializeFromObject({
               "target":"_ComboBoxTextInputSkin_SolidColor1",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_ComboBoxTextInputSkin_SolidColor2",
               "name":"alpha",
               "value":0.01
            }),new SetProperty().initializeFromObject({
               "target":"_ComboBoxTextInputSkin_SolidColor2",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_ComboBoxTextInputSkin_Rect4",
               "name":"top",
               "value":2
            }),new SetProperty().initializeFromObject({
               "target":"bgFill",
               "name":"alpha",
               "value":0.03
            }),new SetProperty().initializeFromObject({
               "target":"bgFill",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_ComboBoxTextInputSkin_SolidColor4",
               "name":"alpha",
               "value":0.2
            }),new SetProperty().initializeFromObject({
               "target":"_ComboBoxTextInputSkin_SolidColorStroke2",
               "name":"color",
               "value":1118481
            })]
         }),new State({
            "name":"normalWithPrompt",
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_loc1_,
               "destination":null,
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["textDisplay"]
            }),new SetProperty().initializeFromObject({
               "target":"_ComboBoxTextInputSkin_SolidColorStroke1",
               "name":"alpha",
               "value":0.1
            }),new SetProperty().initializeFromObject({
               "target":"_ComboBoxTextInputSkin_SolidColor1",
               "name":"alpha",
               "value":0.1
            }),new SetProperty().initializeFromObject({
               "target":"_ComboBoxTextInputSkin_SolidColor1",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_ComboBoxTextInputSkin_SolidColor2",
               "name":"alpha",
               "value":0.15
            }),new SetProperty().initializeFromObject({
               "target":"_ComboBoxTextInputSkin_SolidColor2",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_ComboBoxTextInputSkin_Rect4",
               "name":"top",
               "value":3
            }),new SetProperty().initializeFromObject({
               "target":"bgFill",
               "name":"alpha",
               "value":0.08
            }),new SetProperty().initializeFromObject({
               "target":"bgFill",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_ComboBoxTextInputSkin_SolidColor4",
               "name":"alpha",
               "value":0.3
            }),new SetProperty().initializeFromObject({
               "target":"_ComboBoxTextInputSkin_SolidColorStroke2",
               "name":"color",
               "value":0
            })]
         }),new State({
            "name":"disabledWithPrompt",
            "stateGroups":["disabledStates"],
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_loc1_,
               "destination":null,
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["textDisplay"]
            }),new SetProperty().initializeFromObject({
               "target":"_ComboBoxTextInputSkin_SolidColorStroke1",
               "name":"alpha",
               "value":0.08
            }),new SetProperty().initializeFromObject({
               "target":"_ComboBoxTextInputSkin_SolidColor1",
               "name":"alpha",
               "value":0.03
            }),new SetProperty().initializeFromObject({
               "target":"_ComboBoxTextInputSkin_SolidColor1",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_ComboBoxTextInputSkin_SolidColor2",
               "name":"alpha",
               "value":0.01
            }),new SetProperty().initializeFromObject({
               "target":"_ComboBoxTextInputSkin_SolidColor2",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_ComboBoxTextInputSkin_Rect4",
               "name":"top",
               "value":2
            }),new SetProperty().initializeFromObject({
               "target":"bgFill",
               "name":"alpha",
               "value":0.03
            }),new SetProperty().initializeFromObject({
               "target":"bgFill",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_ComboBoxTextInputSkin_SolidColor4",
               "name":"alpha",
               "value":0.2
            }),new SetProperty().initializeFromObject({
               "target":"_ComboBoxTextInputSkin_SolidColorStroke2",
               "name":"color",
               "value":1118481
            })]
         })];
      }
      
      override public function set moduleFactory(param1:IFlexModuleFactory) : void
      {
         super.moduleFactory = param1;
         if(this.__moduleFactoryInitialized)
         {
            return;
         }
         this.__moduleFactoryInitialized = true;
      }
      
      override public function initialize() : void
      {
         super.initialize();
      }
      
      override public function get colorizeExclusions() : Array
      {
         return exclusions;
      }
      
      override public function get contentItems() : Array
      {
         return contentFill;
      }
      
      override protected function commitProperties() : void
      {
         super.commitProperties();
         if(this.paddingChanged)
         {
            this.updatePadding();
            this.paddingChanged = false;
         }
      }
      
      override protected function initializationComplete() : void
      {
         useChromeColor = true;
         super.initializationComplete();
      }
      
      override protected function updateDisplayList(param1:Number, param2:Number) : void
      {
         if(getStyle("borderVisible") == true)
         {
            this.textDisplay.left = this.textDisplay.right = 1;
            if(this.promptDisplay)
            {
               this.promptDisplay.setLayoutBoundsSize(param1 - 2,param2 - 2);
               this.promptDisplay.setLayoutBoundsPosition(1,1);
            }
         }
         else
         {
            this.textDisplay.left = this.textDisplay.right = 0;
            if(this.promptDisplay)
            {
               this.promptDisplay.setLayoutBoundsSize(param1,param2);
               this.promptDisplay.setLayoutBoundsPosition(0,0);
            }
         }
         super.updateDisplayList(param1,param2);
      }
      
      private function updatePadding() : void
      {
         var _loc1_:Number = NaN;
         if(!this.textDisplay)
         {
            return;
         }
         _loc1_ = getStyle("paddingLeft");
         if(this.textDisplay.getStyle("paddingLeft") != _loc1_)
         {
            this.textDisplay.setStyle("paddingLeft",_loc1_);
         }
         _loc1_ = getStyle("paddingTop");
         if(this.textDisplay.getStyle("paddingTop") != _loc1_)
         {
            this.textDisplay.setStyle("paddingTop",_loc1_);
         }
         _loc1_ = getStyle("paddingRight");
         if(this.textDisplay.getStyle("paddingRight") != _loc1_)
         {
            this.textDisplay.setStyle("paddingRight",_loc1_);
         }
         _loc1_ = getStyle("paddingBottom");
         if(this.textDisplay.getStyle("paddingBottom") != _loc1_)
         {
            this.textDisplay.setStyle("paddingBottom",_loc1_);
         }
         if(!this.promptDisplay)
         {
            return;
         }
         _loc1_ = getStyle("paddingLeft");
         if(this.promptDisplay.getStyle("paddingLeft") != _loc1_)
         {
            this.promptDisplay.setStyle("paddingLeft",_loc1_);
         }
         _loc1_ = getStyle("paddingTop");
         if(this.promptDisplay.getStyle("paddingTop") != _loc1_)
         {
            this.promptDisplay.setStyle("paddingTop",_loc1_);
         }
         _loc1_ = getStyle("paddingRight");
         if(this.promptDisplay.getStyle("paddingRight") != _loc1_)
         {
            this.promptDisplay.setStyle("paddingRight",_loc1_);
         }
         _loc1_ = getStyle("paddingBottom");
         if(this.promptDisplay.getStyle("paddingBottom") != _loc1_)
         {
            this.promptDisplay.setStyle("paddingBottom",_loc1_);
         }
      }
      
      override public function styleChanged(param1:String) : void
      {
         var _loc2_:Boolean = !param1 || param1 == "styleName";
         super.styleChanged(param1);
         if(_loc2_ || param1.indexOf("padding") == 0)
         {
            this.paddingChanged = true;
            invalidateProperties();
         }
      }
      
      override public function get focusSkinExclusions() : Array
      {
         return focusExclusions;
      }
      
      private function _ComboBoxTextInputSkin_Rect1_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 0;
         _loc1_.right = 0;
         _loc1_.top = 0;
         _loc1_.bottom = 0;
         _loc1_.stroke = this._ComboBoxTextInputSkin_SolidColorStroke1_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _ComboBoxTextInputSkin_SolidColorStroke1_i() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.caps = "square";
         _loc1_.color = 16777215;
         _loc1_.joints = "miter";
         this._ComboBoxTextInputSkin_SolidColorStroke1 = _loc1_;
         BindingManager.executeBindings(this,"_ComboBoxTextInputSkin_SolidColorStroke1",this._ComboBoxTextInputSkin_SolidColorStroke1);
         return _loc1_;
      }
      
      private function _ComboBoxTextInputSkin_Rect2_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 2;
         _loc1_.right = 2;
         _loc1_.bottom = 4;
         _loc1_.height = 3;
         _loc1_.fill = this._ComboBoxTextInputSkin_SolidColor1_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _ComboBoxTextInputSkin_SolidColor1_i() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         this._ComboBoxTextInputSkin_SolidColor1 = _loc1_;
         BindingManager.executeBindings(this,"_ComboBoxTextInputSkin_SolidColor1",this._ComboBoxTextInputSkin_SolidColor1);
         return _loc1_;
      }
      
      private function _ComboBoxTextInputSkin_Rect3_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 2;
         _loc1_.right = 2;
         _loc1_.bottom = 2;
         _loc1_.height = 2;
         _loc1_.fill = this._ComboBoxTextInputSkin_SolidColor2_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _ComboBoxTextInputSkin_SolidColor2_i() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         this._ComboBoxTextInputSkin_SolidColor2 = _loc1_;
         BindingManager.executeBindings(this,"_ComboBoxTextInputSkin_SolidColor2",this._ComboBoxTextInputSkin_SolidColor2);
         return _loc1_;
      }
      
      private function _ComboBoxTextInputSkin_Rect4_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 2;
         _loc1_.right = 2;
         _loc1_.bottom = 7;
         _loc1_.fill = this._ComboBoxTextInputSkin_SolidColor3_i();
         _loc1_.initialized(this,"_ComboBoxTextInputSkin_Rect4");
         this._ComboBoxTextInputSkin_Rect4 = _loc1_;
         BindingManager.executeBindings(this,"_ComboBoxTextInputSkin_Rect4",this._ComboBoxTextInputSkin_Rect4);
         return _loc1_;
      }
      
      private function _ComboBoxTextInputSkin_SolidColor3_i() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         this.bgFill = _loc1_;
         BindingManager.executeBindings(this,"bgFill",this.bgFill);
         return _loc1_;
      }
      
      private function _ComboBoxTextInputSkin_Rect5_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 2;
         _loc1_.right = 2;
         _loc1_.top = 2;
         _loc1_.height = 1;
         _loc1_.alpha = 1;
         _loc1_.fill = this._ComboBoxTextInputSkin_SolidColor4_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _ComboBoxTextInputSkin_SolidColor4_i() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 0;
         this._ComboBoxTextInputSkin_SolidColor4 = _loc1_;
         BindingManager.executeBindings(this,"_ComboBoxTextInputSkin_SolidColor4",this._ComboBoxTextInputSkin_SolidColor4);
         return _loc1_;
      }
      
      private function _ComboBoxTextInputSkin_Rect6_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 1;
         _loc1_.right = 1;
         _loc1_.top = 1;
         _loc1_.bottom = 1;
         _loc1_.stroke = this._ComboBoxTextInputSkin_SolidColorStroke2_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _ComboBoxTextInputSkin_SolidColorStroke2_i() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.alpha = 1;
         _loc1_.caps = "square";
         _loc1_.joints = "miter";
         _loc1_.miterLimit = 4;
         _loc1_.weight = 1;
         this._ComboBoxTextInputSkin_SolidColorStroke2 = _loc1_;
         BindingManager.executeBindings(this,"_ComboBoxTextInputSkin_SolidColorStroke2",this._ComboBoxTextInputSkin_SolidColorStroke2);
         return _loc1_;
      }
      
      private function _ComboBoxTextInputSkin_Rect7_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 2;
         _loc1_.top = 3;
         _loc1_.bottom = 1;
         _loc1_.width = 1;
         _loc1_.fill = this._ComboBoxTextInputSkin_LinearGradient1_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _ComboBoxTextInputSkin_LinearGradient1_c() : LinearGradient
      {
         var _loc1_:LinearGradient = new LinearGradient();
         _loc1_.rotation = 90;
         _loc1_.entries = [this._ComboBoxTextInputSkin_GradientEntry1_c(),this._ComboBoxTextInputSkin_GradientEntry2_c()];
         return _loc1_;
      }
      
      private function _ComboBoxTextInputSkin_GradientEntry1_c() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.alpha = 0.2;
         _loc1_.color = 0;
         _loc1_.ratio = 0;
         return _loc1_;
      }
      
      private function _ComboBoxTextInputSkin_GradientEntry2_c() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.alpha = 0;
         _loc1_.color = 0;
         _loc1_.ratio = 1;
         return _loc1_;
      }
      
      private function _ComboBoxTextInputSkin_Rect8_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.right = 2;
         _loc1_.top = 3;
         _loc1_.bottom = 1;
         _loc1_.width = 1;
         _loc1_.fill = this._ComboBoxTextInputSkin_LinearGradient2_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _ComboBoxTextInputSkin_LinearGradient2_c() : LinearGradient
      {
         var _loc1_:LinearGradient = new LinearGradient();
         _loc1_.rotation = 90;
         _loc1_.entries = [this._ComboBoxTextInputSkin_GradientEntry3_c(),this._ComboBoxTextInputSkin_GradientEntry4_c()];
         return _loc1_;
      }
      
      private function _ComboBoxTextInputSkin_GradientEntry3_c() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.alpha = 0.2;
         _loc1_.color = 0;
         _loc1_.ratio = 0;
         return _loc1_;
      }
      
      private function _ComboBoxTextInputSkin_GradientEntry4_c() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.alpha = 0;
         _loc1_.color = 0;
         _loc1_.ratio = 1;
         return _loc1_;
      }
      
      private function _ComboBoxTextInputSkin_RichEditableText1_i() : RichEditableText
      {
         var _loc1_:RichEditableText = new RichEditableText();
         _loc1_.lineBreak = "explicit";
         _loc1_.widthInChars = 10;
         _loc1_.verticalCenter = 0;
         _loc1_.id = "textDisplay";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         this.textDisplay = _loc1_;
         BindingManager.executeBindings(this,"textDisplay",this.textDisplay);
         return _loc1_;
      }
      
      private function _ComboBoxTextInputSkin_Label1_i() : Label
      {
         var _loc1_:Label = new Label();
         _loc1_.maxDisplayedLines = 1;
         _loc1_.mouseEnabled = false;
         _loc1_.mouseChildren = false;
         _loc1_.includeInLayout = false;
         _loc1_.setStyle("verticalAlign","middle");
         _loc1_.id = "promptDisplay";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         this.promptDisplay = _loc1_;
         BindingManager.executeBindings(this,"promptDisplay",this.promptDisplay);
         return _loc1_;
      }
      
      [Bindable(event="propertyChange")]
      public function get _ComboBoxTextInputSkin_Rect4() : Rect
      {
         return this._185313135_ComboBoxTextInputSkin_Rect4;
      }
      
      public function set _ComboBoxTextInputSkin_Rect4(param1:Rect) : void
      {
         var _loc2_:Object = this._185313135_ComboBoxTextInputSkin_Rect4;
         if(_loc2_ !== param1)
         {
            this._185313135_ComboBoxTextInputSkin_Rect4 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_ComboBoxTextInputSkin_Rect4",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _ComboBoxTextInputSkin_SolidColor1() : SolidColor
      {
         return this._1164774120_ComboBoxTextInputSkin_SolidColor1;
      }
      
      public function set _ComboBoxTextInputSkin_SolidColor1(param1:SolidColor) : void
      {
         var _loc2_:Object = this._1164774120_ComboBoxTextInputSkin_SolidColor1;
         if(_loc2_ !== param1)
         {
            this._1164774120_ComboBoxTextInputSkin_SolidColor1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_ComboBoxTextInputSkin_SolidColor1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _ComboBoxTextInputSkin_SolidColor2() : SolidColor
      {
         return this._1164774119_ComboBoxTextInputSkin_SolidColor2;
      }
      
      public function set _ComboBoxTextInputSkin_SolidColor2(param1:SolidColor) : void
      {
         var _loc2_:Object = this._1164774119_ComboBoxTextInputSkin_SolidColor2;
         if(_loc2_ !== param1)
         {
            this._1164774119_ComboBoxTextInputSkin_SolidColor2 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_ComboBoxTextInputSkin_SolidColor2",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _ComboBoxTextInputSkin_SolidColor4() : SolidColor
      {
         return this._1164774117_ComboBoxTextInputSkin_SolidColor4;
      }
      
      public function set _ComboBoxTextInputSkin_SolidColor4(param1:SolidColor) : void
      {
         var _loc2_:Object = this._1164774117_ComboBoxTextInputSkin_SolidColor4;
         if(_loc2_ !== param1)
         {
            this._1164774117_ComboBoxTextInputSkin_SolidColor4 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_ComboBoxTextInputSkin_SolidColor4",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _ComboBoxTextInputSkin_SolidColorStroke1() : SolidColorStroke
      {
         return this._610096256_ComboBoxTextInputSkin_SolidColorStroke1;
      }
      
      public function set _ComboBoxTextInputSkin_SolidColorStroke1(param1:SolidColorStroke) : void
      {
         var _loc2_:Object = this._610096256_ComboBoxTextInputSkin_SolidColorStroke1;
         if(_loc2_ !== param1)
         {
            this._610096256_ComboBoxTextInputSkin_SolidColorStroke1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_ComboBoxTextInputSkin_SolidColorStroke1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _ComboBoxTextInputSkin_SolidColorStroke2() : SolidColorStroke
      {
         return this._610096257_ComboBoxTextInputSkin_SolidColorStroke2;
      }
      
      public function set _ComboBoxTextInputSkin_SolidColorStroke2(param1:SolidColorStroke) : void
      {
         var _loc2_:Object = this._610096257_ComboBoxTextInputSkin_SolidColorStroke2;
         if(_loc2_ !== param1)
         {
            this._610096257_ComboBoxTextInputSkin_SolidColorStroke2 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_ComboBoxTextInputSkin_SolidColorStroke2",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get bgFill() : SolidColor
      {
         return this._1391998104bgFill;
      }
      
      public function set bgFill(param1:SolidColor) : void
      {
         var _loc2_:Object = this._1391998104bgFill;
         if(_loc2_ !== param1)
         {
            this._1391998104bgFill = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"bgFill",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get promptDisplay() : Label
      {
         return this._263438014promptDisplay;
      }
      
      public function set promptDisplay(param1:Label) : void
      {
         var _loc2_:Object = this._263438014promptDisplay;
         if(_loc2_ !== param1)
         {
            this._263438014promptDisplay = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"promptDisplay",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get textDisplay() : RichEditableText
      {
         return this._831827669textDisplay;
      }
      
      public function set textDisplay(param1:RichEditableText) : void
      {
         var _loc2_:Object = this._831827669textDisplay;
         if(_loc2_ !== param1)
         {
            this._831827669textDisplay = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"textDisplay",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get hostComponent() : TextInput
      {
         return this._213507019hostComponent;
      }
      
      public function set hostComponent(param1:TextInput) : void
      {
         var _loc2_:Object = this._213507019hostComponent;
         if(_loc2_ !== param1)
         {
            this._213507019hostComponent = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"hostComponent",_loc2_,param1));
            }
         }
      }
   }
}

