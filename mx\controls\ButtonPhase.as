package mx.controls
{
   import mx.core.mx_internal;
   
   use namespace mx_internal;
   
   public final class ButtonPhase
   {
      
      mx_internal static const VERSION:String = "4.6.0.23201";
      
      public static const DOWN:String = "down";
      
      public static const OVER:String = "over";
      
      public static const UP:String = "up";
      
      public function ButtonPhase()
      {
         super();
      }
   }
}

