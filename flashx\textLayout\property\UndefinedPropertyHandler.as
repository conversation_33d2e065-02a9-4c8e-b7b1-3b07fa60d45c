package flashx.textLayout.property
{
   public class Undefined<PERSON><PERSON>tyHandler extends PropertyHandler
   {
      
      public function UndefinedPropertyHandler()
      {
         super();
      }
      
      override public function owningHandlerCheck(param1:*) : *
      {
         return param1 === null || param1 === undefined ? true : undefined;
      }
      
      override public function setH<PERSON>per(param1:*) : *
      {
         return undefined;
      }
   }
}

