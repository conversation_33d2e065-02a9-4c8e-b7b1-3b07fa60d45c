package com.kingnare.skin.mx.ComboBox
{
   import com.kingnare.skin.mx.util.DrawUtil;
   import flash.display.Graphics;
   import mx.skins.Border;
   
   public class ComboBoxSkin extends Border
   {
      
      public function ComboBoxSkin()
      {
         super();
      }
      
      override public function get measuredWidth() : Number
      {
         return 22;
      }
      
      override public function get measuredHeight() : Number
      {
         return 22;
      }
      
      override protected function updateDisplayList(param1:Number, param2:Number) : void
      {
         var _loc3_:Graphics = graphics;
         _loc3_.clear();
         var _loc4_:Array = [0.1,0.1];
         var _loc5_:Array = [16777215,16777215];
         var _loc6_:Array = [0.08,0.03];
         var _loc7_:Array = [8421504,8421504];
         var _loc8_:Boolean = true;
         if(name.indexOf("editable") < 0)
         {
            _loc8_ = false;
         }
         switch(name)
         {
            case "editableUpSkin":
            case "upSkin":
               break;
            case "editableOverSkin":
            case "overSkin":
               _loc6_ = [0.15,0.05];
               _loc7_ = [16777215,16777215];
               break;
            case "editableDownSkin":
            case "downSkin":
               _loc4_ = [0.15,0.15];
               _loc6_ = [0.05,0];
               _loc7_ = [13421772,13421772];
               break;
            case "editableDisabledSkin":
            case "disabledSkin":
               _loc4_ = [0.05,0.05];
               _loc5_ = [0,0];
               _loc6_ = [0.3,0.3];
               _loc7_ = [0,0];
         }
         if(!_loc8_)
         {
            DrawUtil.drawDoubleRect(_loc3_,[16777215,16777215],_loc4_,0,0,param1,param2,1,1,param1 - 2,param2 - 2);
            DrawUtil.drawDoubleRect(_loc3_,[0,0],[0.6,0.6],1,1,param1 - 2,param2 - 2,2,2,param1 - 4,param2 - 4);
            DrawUtil.drawSingleRect(_loc3_,_loc5_,_loc6_,2,2,param1 - 4,param2 - 4);
            DrawUtil.drawDoubleRect(_loc3_,[16777215,16777215],[0.08,0.03],2,2,param1 - 4,param2 - 4,3,3,param1 - 6,param2 - 6);
            DrawUtil.drawSingleRect(_loc3_,[0,0],[0.85,0.6],param1 - 18,4,1,param2 - 8);
            DrawUtil.drawSingleRect(_loc3_,[16777215,16777215],[0.15,0.05],param1 - 18 + 1,4,1,param2 - 8);
            DrawUtil.drawArrow(_loc3_,5,_loc7_,[1,1],param1 - 13,Math.floor(param2 / 2) - Math.floor(5 / 2));
         }
         else
         {
            DrawUtil.drawDoubleRect(_loc3_,[16777215,16777215],_loc4_,0,0,param1,param2,0,1,param1 - 1,param2 - 2);
            DrawUtil.drawDoubleRect(_loc3_,[0,0],[0.6,0.6],0,1,param1 - 1,param2 - 2,0,2,param1 - 2,param2 - 4);
            DrawUtil.drawSingleRect(_loc3_,_loc5_,_loc6_,0,2,param1 - 2,param2 - 4);
            DrawUtil.drawDoubleRect(_loc3_,[16777215,16777215],[0.08,0.03],0,2,param1 - 2,param2 - 4,1,3,param1 - 4,param2 - 6);
            DrawUtil.drawArrow(_loc3_,5,_loc7_,[1,1],Math.floor((param1 - 1 - 7) / 2),Math.floor(param2 / 2) - Math.floor(5 / 2));
         }
      }
   }
}

