package com.kingnare.skin.spark.VScrollbar
{
   import mx.binding.BindingManager;
   import mx.core.IFlexModuleFactory;
   import mx.core.IStateClient2;
   import mx.events.PropertyChangeEvent;
   import mx.graphics.SolidColor;
   import mx.graphics.SolidColorStroke;
   import mx.states.SetProperty;
   import mx.states.State;
   import spark.components.Button;
   import spark.components.Group;
   import spark.components.supportClasses.Skin;
   import spark.filters.DropShadowFilter;
   import spark.primitives.Rect;
   
   public class VScrollbarDownButton extends Skin implements IStateClient2
   {
      
      private var _882517390_VScrollbarDownButton_Group1:Group;
      
      private var _1318968091_VScrollbarDownButton_SolidColorStroke1:SolidColorStroke;
      
      private var __moduleFactoryInitialized:Boolean = false;
      
      private var _213507019hostComponent:Button;
      
      public function VScrollbarDownButton()
      {
         super();
         mx_internal::_document = this;
         this.width = 13;
         this.height = 18;
         this.mxmlContent = [this._VScrollbarDownButton_Rect1_c(),this._VScrollbarDownButton_Rect2_c(),this._VScrollbarDownButton_Rect3_c(),this._VScrollbarDownButton_Group1_i()];
         this.currentState = "up";
         states = [new State({
            "name":"up",
            "overrides":[]
         }),new State({
            "name":"over",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_VScrollbarDownButton_Group1",
               "name":"alpha",
               "value":1
            })]
         }),new State({
            "name":"down",
            "overrides":[]
         }),new State({
            "name":"disabled",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_VScrollbarDownButton_SolidColorStroke1",
               "name":"color",
               "value":3355443
            })]
         })];
      }
      
      override public function set moduleFactory(param1:IFlexModuleFactory) : void
      {
         super.moduleFactory = param1;
         if(this.__moduleFactoryInitialized)
         {
            return;
         }
         this.__moduleFactoryInitialized = true;
      }
      
      override public function initialize() : void
      {
         super.initialize();
      }
      
      private function _VScrollbarDownButton_Rect1_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.x = 0.5;
         _loc1_.y = 0.5;
         _loc1_.width = 12;
         _loc1_.height = 17;
         _loc1_.stroke = this._VScrollbarDownButton_SolidColorStroke1_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _VScrollbarDownButton_SolidColorStroke1_i() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.alpha = 0.6;
         _loc1_.caps = "square";
         _loc1_.color = 0;
         _loc1_.joints = "miter";
         this._VScrollbarDownButton_SolidColorStroke1 = _loc1_;
         BindingManager.executeBindings(this,"_VScrollbarDownButton_SolidColorStroke1",this._VScrollbarDownButton_SolidColorStroke1);
         return _loc1_;
      }
      
      private function _VScrollbarDownButton_Rect2_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.x = 1.5;
         _loc1_.y = 1.5;
         _loc1_.width = 10;
         _loc1_.height = 15;
         _loc1_.stroke = this._VScrollbarDownButton_SolidColorStroke2_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _VScrollbarDownButton_SolidColorStroke2_c() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.alpha = 0.15;
         _loc1_.caps = "square";
         _loc1_.color = 16777215;
         _loc1_.joints = "miter";
         return _loc1_;
      }
      
      private function _VScrollbarDownButton_Rect3_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.x = 2;
         _loc1_.y = 2;
         _loc1_.width = 9;
         _loc1_.height = 14;
         _loc1_.fill = this._VScrollbarDownButton_SolidColor1_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _VScrollbarDownButton_SolidColor1_c() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.alpha = 0.05;
         _loc1_.color = 16777215;
         return _loc1_;
      }
      
      private function _VScrollbarDownButton_Group1_i() : Group
      {
         var _loc1_:Group = new Group();
         _loc1_.alpha = 0.5;
         _loc1_.horizontalCenter = 0;
         _loc1_.verticalCenter = 0.5;
         _loc1_.filters = [this._VScrollbarDownButton_DropShadowFilter1_c()];
         _loc1_.mxmlContent = [this._VScrollbarDownButton_Rect4_c(),this._VScrollbarDownButton_Rect5_c(),this._VScrollbarDownButton_Rect6_c()];
         _loc1_.id = "_VScrollbarDownButton_Group1";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         this._VScrollbarDownButton_Group1 = _loc1_;
         BindingManager.executeBindings(this,"_VScrollbarDownButton_Group1",this._VScrollbarDownButton_Group1);
         return _loc1_;
      }
      
      private function _VScrollbarDownButton_DropShadowFilter1_c() : DropShadowFilter
      {
         var _loc1_:DropShadowFilter = new DropShadowFilter();
         _loc1_.alpha = 0.5;
         _loc1_.angle = 90;
         _loc1_.blurX = 1;
         _loc1_.blurY = 1;
         _loc1_.color = 0;
         _loc1_.distance = 1;
         _loc1_.hideObject = false;
         _loc1_.inner = false;
         _loc1_.knockout = false;
         _loc1_.quality = 1;
         _loc1_.strength = 4;
         return _loc1_;
      }
      
      private function _VScrollbarDownButton_Rect4_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.x = 2;
         _loc1_.y = 2;
         _loc1_.width = 1;
         _loc1_.height = 1;
         _loc1_.fill = this._VScrollbarDownButton_SolidColor2_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _VScrollbarDownButton_SolidColor2_c() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 16777215;
         return _loc1_;
      }
      
      private function _VScrollbarDownButton_Rect5_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.x = 1;
         _loc1_.y = 1;
         _loc1_.width = 3;
         _loc1_.height = 1;
         _loc1_.fill = this._VScrollbarDownButton_SolidColor3_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _VScrollbarDownButton_SolidColor3_c() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 16777215;
         return _loc1_;
      }
      
      private function _VScrollbarDownButton_Rect6_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.x = 0;
         _loc1_.y = 0;
         _loc1_.width = 5;
         _loc1_.height = 1;
         _loc1_.fill = this._VScrollbarDownButton_SolidColor4_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _VScrollbarDownButton_SolidColor4_c() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 16777215;
         return _loc1_;
      }
      
      [Bindable(event="propertyChange")]
      public function get _VScrollbarDownButton_Group1() : Group
      {
         return this._882517390_VScrollbarDownButton_Group1;
      }
      
      public function set _VScrollbarDownButton_Group1(param1:Group) : void
      {
         var _loc2_:Object = this._882517390_VScrollbarDownButton_Group1;
         if(_loc2_ !== param1)
         {
            this._882517390_VScrollbarDownButton_Group1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_VScrollbarDownButton_Group1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _VScrollbarDownButton_SolidColorStroke1() : SolidColorStroke
      {
         return this._1318968091_VScrollbarDownButton_SolidColorStroke1;
      }
      
      public function set _VScrollbarDownButton_SolidColorStroke1(param1:SolidColorStroke) : void
      {
         var _loc2_:Object = this._1318968091_VScrollbarDownButton_SolidColorStroke1;
         if(_loc2_ !== param1)
         {
            this._1318968091_VScrollbarDownButton_SolidColorStroke1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_VScrollbarDownButton_SolidColorStroke1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get hostComponent() : Button
      {
         return this._213507019hostComponent;
      }
      
      public function set hostComponent(param1:Button) : void
      {
         var _loc2_:Object = this._213507019hostComponent;
         if(_loc2_ !== param1)
         {
            this._213507019hostComponent = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"hostComponent",_loc2_,param1));
            }
         }
      }
   }
}

