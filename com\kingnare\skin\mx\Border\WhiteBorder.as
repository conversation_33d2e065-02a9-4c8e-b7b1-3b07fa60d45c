package com.kingnare.skin.mx.Border
{
   import mx.binding.BindingManager;
   import mx.core.IFlexModuleFactory;
   import mx.core.IStateClient2;
   import mx.events.PropertyChangeEvent;
   import mx.graphics.SolidColor;
   import mx.graphics.SolidColorStroke;
   import mx.states.State;
   import spark.components.BorderContainer;
   import spark.components.Group;
   import spark.components.supportClasses.Skin;
   import spark.layouts.BasicLayout;
   import spark.primitives.Rect;
   
   public class WhiteBorder extends Skin implements IStateClient2
   {
      
      private var _1332194002background:Rect;
      
      private var _1391998104bgFill:SolidColor;
      
      private var _809612678contentGroup:Group;
      
      private var _156423678innerborder:Rect;
      
      private var _1683616665outerborder:Rect;
      
      private var __moduleFactoryInitialized:Boolean = false;
      
      private var _213507019hostComponent:BorderContainer;
      
      public function WhiteBorder()
      {
         super();
         mx_internal::_document = this;
         this.mxmlContent = [this._WhiteBorder_Rect1_i(),this._WhiteBorder_Rect2_i(),this._WhiteBorder_Rect3_i(),this._WhiteBorder_Group1_i()];
         this.currentState = "disabled";
         states = [new State({
            "name":"disabled",
            "overrides":[]
         }),new State({
            "name":"normal",
            "overrides":[]
         })];
      }
      
      override public function set moduleFactory(param1:IFlexModuleFactory) : void
      {
         super.moduleFactory = param1;
         if(this.__moduleFactoryInitialized)
         {
            return;
         }
         this.__moduleFactoryInitialized = true;
      }
      
      override public function initialize() : void
      {
         super.initialize();
      }
      
      override protected function updateDisplayList(param1:Number, param2:Number) : void
      {
         super.updateDisplayList(param1,param2);
         this.bgFill.color = getStyle("backgroundColor");
      }
      
      private function _WhiteBorder_Rect1_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = -2;
         _loc1_.right = -2;
         _loc1_.top = -2;
         _loc1_.bottom = -2;
         _loc1_.radiusX = 0;
         _loc1_.stroke = this._WhiteBorder_SolidColorStroke1_c();
         _loc1_.initialized(this,"outerborder");
         this.outerborder = _loc1_;
         BindingManager.executeBindings(this,"outerborder",this.outerborder);
         return _loc1_;
      }
      
      private function _WhiteBorder_SolidColorStroke1_c() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.color = 16777215;
         _loc1_.alpha = 0.12;
         return _loc1_;
      }
      
      private function _WhiteBorder_Rect2_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = -1;
         _loc1_.right = -1;
         _loc1_.top = -1;
         _loc1_.bottom = -1;
         _loc1_.radiusX = 0;
         _loc1_.stroke = this._WhiteBorder_SolidColorStroke2_c();
         _loc1_.initialized(this,"innerborder");
         this.innerborder = _loc1_;
         BindingManager.executeBindings(this,"innerborder",this.innerborder);
         return _loc1_;
      }
      
      private function _WhiteBorder_SolidColorStroke2_c() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.color = 0;
         _loc1_.alpha = 1;
         return _loc1_;
      }
      
      private function _WhiteBorder_Rect3_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 0;
         _loc1_.right = 0;
         _loc1_.top = 0;
         _loc1_.bottom = 0;
         _loc1_.radiusX = 0;
         _loc1_.fill = this._WhiteBorder_SolidColor1_i();
         _loc1_.initialized(this,"background");
         this.background = _loc1_;
         BindingManager.executeBindings(this,"background",this.background);
         return _loc1_;
      }
      
      private function _WhiteBorder_SolidColor1_i() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 2236962;
         this.bgFill = _loc1_;
         BindingManager.executeBindings(this,"bgFill",this.bgFill);
         return _loc1_;
      }
      
      private function _WhiteBorder_Group1_i() : Group
      {
         var _loc1_:Group = new Group();
         _loc1_.left = 0;
         _loc1_.right = 0;
         _loc1_.top = 0;
         _loc1_.bottom = 0;
         _loc1_.minWidth = 0;
         _loc1_.minHeight = 0;
         _loc1_.layout = this._WhiteBorder_BasicLayout1_c();
         _loc1_.id = "contentGroup";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         this.contentGroup = _loc1_;
         BindingManager.executeBindings(this,"contentGroup",this.contentGroup);
         return _loc1_;
      }
      
      private function _WhiteBorder_BasicLayout1_c() : BasicLayout
      {
         return new BasicLayout();
      }
      
      [Bindable(event="propertyChange")]
      public function get background() : Rect
      {
         return this._1332194002background;
      }
      
      public function set background(param1:Rect) : void
      {
         var _loc2_:Object = this._1332194002background;
         if(_loc2_ !== param1)
         {
            this._1332194002background = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"background",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get bgFill() : SolidColor
      {
         return this._1391998104bgFill;
      }
      
      public function set bgFill(param1:SolidColor) : void
      {
         var _loc2_:Object = this._1391998104bgFill;
         if(_loc2_ !== param1)
         {
            this._1391998104bgFill = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"bgFill",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get contentGroup() : Group
      {
         return this._809612678contentGroup;
      }
      
      public function set contentGroup(param1:Group) : void
      {
         var _loc2_:Object = this._809612678contentGroup;
         if(_loc2_ !== param1)
         {
            this._809612678contentGroup = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"contentGroup",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get innerborder() : Rect
      {
         return this._156423678innerborder;
      }
      
      public function set innerborder(param1:Rect) : void
      {
         var _loc2_:Object = this._156423678innerborder;
         if(_loc2_ !== param1)
         {
            this._156423678innerborder = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"innerborder",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get outerborder() : Rect
      {
         return this._1683616665outerborder;
      }
      
      public function set outerborder(param1:Rect) : void
      {
         var _loc2_:Object = this._1683616665outerborder;
         if(_loc2_ !== param1)
         {
            this._1683616665outerborder = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"outerborder",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get hostComponent() : BorderContainer
      {
         return this._213507019hostComponent;
      }
      
      public function set hostComponent(param1:BorderContainer) : void
      {
         var _loc2_:Object = this._213507019hostComponent;
         if(_loc2_ !== param1)
         {
            this._213507019hostComponent = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"hostComponent",_loc2_,param1));
            }
         }
      }
   }
}

