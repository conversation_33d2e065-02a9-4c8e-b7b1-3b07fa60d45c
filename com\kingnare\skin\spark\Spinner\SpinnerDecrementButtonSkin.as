package com.kingnare.skin.spark.Spinner
{
   import mx.binding.BindingManager;
   import mx.core.IFlexModuleFactory;
   import mx.core.IStateClient2;
   import mx.events.PropertyChangeEvent;
   import mx.graphics.GradientEntry;
   import mx.graphics.LinearGradient;
   import mx.graphics.LinearGradientStroke;
   import mx.graphics.SolidColor;
   import mx.graphics.SolidColorStroke;
   import mx.states.SetProperty;
   import mx.states.State;
   import spark.components.Button;
   import spark.primitives.Path;
   import spark.primitives.Rect;
   import spark.skins.SparkSkin;
   
   public class SpinnerDecrementButtonSkin extends SparkSkin implements IStateClient2
   {
      
      private static const exclusions:Array = ["arrow"];
      
      private static const symbols:Array = ["arrowFill"];
      
      private var _1096450842_SpinnerDecrementButtonSkin_GradientEntry1:GradientEntry;
      
      private var _1096450841_SpinnerDecrementButtonSkin_GradientEntry2:GradientEntry;
      
      private var _1096450840_SpinnerDecrementButtonSkin_GradientEntry3:GradientEntry;
      
      private var _1096450839_SpinnerDecrementButtonSkin_GradientEntry4:GradientEntry;
      
      private var _1918037142_SpinnerDecrementButtonSkin_SolidColorStroke1:SolidColorStroke;
      
      private var _93090825arrow:Path;
      
      private var _1026379476arrowFill:SolidColor;
      
      private var _1780999580innerLightRect:Rect;
      
      private var _2100685128innerLightStroke:Rect;
      
      private var _528023206innerRect:Rect;
      
      private var _685606938lightRect:Rect;
      
      private var __moduleFactoryInitialized:Boolean = false;
      
      private var cornerRadius:Number = 2;
      
      private var _213507019hostComponent:Button;
      
      public function SpinnerDecrementButtonSkin()
      {
         super();
         mx_internal::_document = this;
         this.mxmlContent = [this._SpinnerDecrementButtonSkin_Rect1_i(),this._SpinnerDecrementButtonSkin_Rect2_i(),this._SpinnerDecrementButtonSkin_Rect3_i(),this._SpinnerDecrementButtonSkin_Rect4_i(),this._SpinnerDecrementButtonSkin_Path1_i()];
         this.currentState = "up";
         states = [new State({
            "name":"up",
            "overrides":[]
         }),new State({
            "name":"over",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_SpinnerDecrementButtonSkin_GradientEntry1",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_SpinnerDecrementButtonSkin_GradientEntry1",
               "name":"alpha",
               "value":0.15
            }),new SetProperty().initializeFromObject({
               "target":"_SpinnerDecrementButtonSkin_GradientEntry2",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_SpinnerDecrementButtonSkin_GradientEntry2",
               "name":"alpha",
               "value":0.05
            }),new SetProperty().initializeFromObject({
               "target":"arrowFill",
               "name":"alpha",
               "value":1
            })]
         }),new State({
            "name":"down",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_SpinnerDecrementButtonSkin_SolidColorStroke1",
               "name":"alpha",
               "value":0.15
            }),new SetProperty().initializeFromObject({
               "target":"_SpinnerDecrementButtonSkin_GradientEntry1",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_SpinnerDecrementButtonSkin_GradientEntry1",
               "name":"alpha",
               "value":0.05
            }),new SetProperty().initializeFromObject({
               "target":"_SpinnerDecrementButtonSkin_GradientEntry2",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_SpinnerDecrementButtonSkin_GradientEntry2",
               "name":"alpha",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_SpinnerDecrementButtonSkin_GradientEntry3",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_SpinnerDecrementButtonSkin_GradientEntry3",
               "name":"alpha",
               "value":0.2
            }),new SetProperty().initializeFromObject({
               "target":"_SpinnerDecrementButtonSkin_GradientEntry4",
               "name":"ratio",
               "value":1
            }),new SetProperty().initializeFromObject({
               "target":"_SpinnerDecrementButtonSkin_GradientEntry4",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_SpinnerDecrementButtonSkin_GradientEntry4",
               "name":"alpha",
               "value":0.1
            }),new SetProperty().initializeFromObject({
               "target":"arrowFill",
               "name":"alpha",
               "value":0.6
            })]
         }),new State({
            "name":"disabled",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_SpinnerDecrementButtonSkin_SolidColorStroke1",
               "name":"alpha",
               "value":0.05
            }),new SetProperty().initializeFromObject({
               "target":"_SpinnerDecrementButtonSkin_GradientEntry1",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_SpinnerDecrementButtonSkin_GradientEntry1",
               "name":"alpha",
               "value":0.3
            }),new SetProperty().initializeFromObject({
               "target":"_SpinnerDecrementButtonSkin_GradientEntry2",
               "name":"color",
               "value":0
            }),new SetProperty().initializeFromObject({
               "target":"_SpinnerDecrementButtonSkin_GradientEntry2",
               "name":"alpha",
               "value":0.3
            }),new SetProperty().initializeFromObject({
               "target":"_SpinnerDecrementButtonSkin_GradientEntry3",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_SpinnerDecrementButtonSkin_GradientEntry3",
               "name":"alpha",
               "value":0.05
            }),new SetProperty().initializeFromObject({
               "target":"_SpinnerDecrementButtonSkin_GradientEntry4",
               "name":"color",
               "value":16777215
            }),new SetProperty().initializeFromObject({
               "target":"_SpinnerDecrementButtonSkin_GradientEntry4",
               "name":"alpha",
               "value":0.05
            }),new SetProperty().initializeFromObject({
               "target":"arrowFill",
               "name":"alpha",
               "value":0.5
            })]
         })];
      }
      
      override public function set moduleFactory(param1:IFlexModuleFactory) : void
      {
         super.moduleFactory = param1;
         if(this.__moduleFactoryInitialized)
         {
            return;
         }
         this.__moduleFactoryInitialized = true;
      }
      
      override public function initialize() : void
      {
         super.initialize();
      }
      
      override public function get colorizeExclusions() : Array
      {
         return exclusions;
      }
      
      override public function get symbolItems() : Array
      {
         return symbols;
      }
      
      override protected function initializationComplete() : void
      {
         useChromeColor = true;
         super.initializationComplete();
      }
      
      override protected function updateDisplayList(param1:Number, param2:Number) : void
      {
         var _loc3_:Number = getStyle("cornerRadius");
         if(this.cornerRadius != _loc3_)
         {
            this.cornerRadius = _loc3_;
         }
         super.updateDisplayList(param1,param2);
      }
      
      private function _SpinnerDecrementButtonSkin_Rect1_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 0;
         _loc1_.right = 0;
         _loc1_.top = 0;
         _loc1_.bottom = 0;
         _loc1_.width = 18;
         _loc1_.height = 16;
         _loc1_.stroke = this._SpinnerDecrementButtonSkin_SolidColorStroke1_i();
         _loc1_.initialized(this,"lightRect");
         this.lightRect = _loc1_;
         BindingManager.executeBindings(this,"lightRect",this.lightRect);
         return _loc1_;
      }
      
      private function _SpinnerDecrementButtonSkin_SolidColorStroke1_i() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.color = 16777215;
         _loc1_.caps = "none";
         _loc1_.joints = "miter";
         _loc1_.miterLimit = 4;
         _loc1_.weight = 1;
         _loc1_.alpha = 0.15;
         this._SpinnerDecrementButtonSkin_SolidColorStroke1 = _loc1_;
         BindingManager.executeBindings(this,"_SpinnerDecrementButtonSkin_SolidColorStroke1",this._SpinnerDecrementButtonSkin_SolidColorStroke1);
         return _loc1_;
      }
      
      private function _SpinnerDecrementButtonSkin_Rect2_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.top = 1;
         _loc1_.bottom = 1;
         _loc1_.right = 1;
         _loc1_.left = 1;
         _loc1_.stroke = this._SpinnerDecrementButtonSkin_SolidColorStroke2_c();
         _loc1_.initialized(this,"innerRect");
         this.innerRect = _loc1_;
         BindingManager.executeBindings(this,"innerRect",this.innerRect);
         return _loc1_;
      }
      
      private function _SpinnerDecrementButtonSkin_SolidColorStroke2_c() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.weight = 1;
         _loc1_.caps = "none";
         _loc1_.miterLimit = 4;
         _loc1_.joints = "miter";
         _loc1_.color = 0;
         _loc1_.alpha = 0.6;
         return _loc1_;
      }
      
      private function _SpinnerDecrementButtonSkin_Rect3_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.top = 2;
         _loc1_.right = 2;
         _loc1_.left = 2;
         _loc1_.bottom = 2;
         _loc1_.fill = this._SpinnerDecrementButtonSkin_LinearGradient1_c();
         _loc1_.initialized(this,"innerLightRect");
         this.innerLightRect = _loc1_;
         BindingManager.executeBindings(this,"innerLightRect",this.innerLightRect);
         return _loc1_;
      }
      
      private function _SpinnerDecrementButtonSkin_LinearGradient1_c() : LinearGradient
      {
         var _loc1_:LinearGradient = new LinearGradient();
         _loc1_.rotation = 90;
         _loc1_.entries = [this._SpinnerDecrementButtonSkin_GradientEntry1_i(),this._SpinnerDecrementButtonSkin_GradientEntry2_i()];
         return _loc1_;
      }
      
      private function _SpinnerDecrementButtonSkin_GradientEntry1_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 16777215;
         _loc1_.alpha = 0.1;
         _loc1_.ratio = 0;
         this._SpinnerDecrementButtonSkin_GradientEntry1 = _loc1_;
         BindingManager.executeBindings(this,"_SpinnerDecrementButtonSkin_GradientEntry1",this._SpinnerDecrementButtonSkin_GradientEntry1);
         return _loc1_;
      }
      
      private function _SpinnerDecrementButtonSkin_GradientEntry2_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 16777215;
         _loc1_.alpha = 0;
         _loc1_.ratio = 1;
         this._SpinnerDecrementButtonSkin_GradientEntry2 = _loc1_;
         BindingManager.executeBindings(this,"_SpinnerDecrementButtonSkin_GradientEntry2",this._SpinnerDecrementButtonSkin_GradientEntry2);
         return _loc1_;
      }
      
      private function _SpinnerDecrementButtonSkin_Rect4_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.top = 2;
         _loc1_.right = 2;
         _loc1_.left = 2;
         _loc1_.bottom = 2;
         _loc1_.stroke = this._SpinnerDecrementButtonSkin_LinearGradientStroke1_c();
         _loc1_.initialized(this,"innerLightStroke");
         this.innerLightStroke = _loc1_;
         BindingManager.executeBindings(this,"innerLightStroke",this.innerLightStroke);
         return _loc1_;
      }
      
      private function _SpinnerDecrementButtonSkin_LinearGradientStroke1_c() : LinearGradientStroke
      {
         var _loc1_:LinearGradientStroke = new LinearGradientStroke();
         _loc1_.weight = 1;
         _loc1_.caps = "none";
         _loc1_.miterLimit = 4;
         _loc1_.joints = "miter";
         _loc1_.rotation = 90;
         _loc1_.entries = [this._SpinnerDecrementButtonSkin_GradientEntry3_i(),this._SpinnerDecrementButtonSkin_GradientEntry4_i()];
         return _loc1_;
      }
      
      private function _SpinnerDecrementButtonSkin_GradientEntry3_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 16777215;
         _loc1_.alpha = 0.08;
         _loc1_.ratio = 0;
         this._SpinnerDecrementButtonSkin_GradientEntry3 = _loc1_;
         BindingManager.executeBindings(this,"_SpinnerDecrementButtonSkin_GradientEntry3",this._SpinnerDecrementButtonSkin_GradientEntry3);
         return _loc1_;
      }
      
      private function _SpinnerDecrementButtonSkin_GradientEntry4_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 16777215;
         _loc1_.alpha = 0.03;
         _loc1_.ratio = 1;
         this._SpinnerDecrementButtonSkin_GradientEntry4 = _loc1_;
         BindingManager.executeBindings(this,"_SpinnerDecrementButtonSkin_GradientEntry4",this._SpinnerDecrementButtonSkin_GradientEntry4);
         return _loc1_;
      }
      
      private function _SpinnerDecrementButtonSkin_Path1_i() : Path
      {
         var _loc1_:Path = new Path();
         _loc1_.horizontalCenter = 0;
         _loc1_.verticalCenter = 0;
         _loc1_.data = "M 3.0 3.0 L 3.0 2.0 L 4.0 2.0 L 4.0 1.0 L 5.0 1.0 L 5.0 0.0 L 0.0 0.0 L 0.0 1.0 L 1.0 1.0 L 1.0 2.0 L 2.0 2.0 L 2.0 3.0 L 3.0 3.0";
         _loc1_.fill = this._SpinnerDecrementButtonSkin_SolidColor1_i();
         _loc1_.initialized(this,"arrow");
         this.arrow = _loc1_;
         BindingManager.executeBindings(this,"arrow",this.arrow);
         return _loc1_;
      }
      
      private function _SpinnerDecrementButtonSkin_SolidColor1_i() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 0;
         _loc1_.alpha = 0.6;
         this.arrowFill = _loc1_;
         BindingManager.executeBindings(this,"arrowFill",this.arrowFill);
         return _loc1_;
      }
      
      [Bindable(event="propertyChange")]
      public function get _SpinnerDecrementButtonSkin_GradientEntry1() : GradientEntry
      {
         return this._1096450842_SpinnerDecrementButtonSkin_GradientEntry1;
      }
      
      public function set _SpinnerDecrementButtonSkin_GradientEntry1(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._1096450842_SpinnerDecrementButtonSkin_GradientEntry1;
         if(_loc2_ !== param1)
         {
            this._1096450842_SpinnerDecrementButtonSkin_GradientEntry1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_SpinnerDecrementButtonSkin_GradientEntry1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _SpinnerDecrementButtonSkin_GradientEntry2() : GradientEntry
      {
         return this._1096450841_SpinnerDecrementButtonSkin_GradientEntry2;
      }
      
      public function set _SpinnerDecrementButtonSkin_GradientEntry2(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._1096450841_SpinnerDecrementButtonSkin_GradientEntry2;
         if(_loc2_ !== param1)
         {
            this._1096450841_SpinnerDecrementButtonSkin_GradientEntry2 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_SpinnerDecrementButtonSkin_GradientEntry2",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _SpinnerDecrementButtonSkin_GradientEntry3() : GradientEntry
      {
         return this._1096450840_SpinnerDecrementButtonSkin_GradientEntry3;
      }
      
      public function set _SpinnerDecrementButtonSkin_GradientEntry3(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._1096450840_SpinnerDecrementButtonSkin_GradientEntry3;
         if(_loc2_ !== param1)
         {
            this._1096450840_SpinnerDecrementButtonSkin_GradientEntry3 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_SpinnerDecrementButtonSkin_GradientEntry3",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _SpinnerDecrementButtonSkin_GradientEntry4() : GradientEntry
      {
         return this._1096450839_SpinnerDecrementButtonSkin_GradientEntry4;
      }
      
      public function set _SpinnerDecrementButtonSkin_GradientEntry4(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._1096450839_SpinnerDecrementButtonSkin_GradientEntry4;
         if(_loc2_ !== param1)
         {
            this._1096450839_SpinnerDecrementButtonSkin_GradientEntry4 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_SpinnerDecrementButtonSkin_GradientEntry4",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _SpinnerDecrementButtonSkin_SolidColorStroke1() : SolidColorStroke
      {
         return this._1918037142_SpinnerDecrementButtonSkin_SolidColorStroke1;
      }
      
      public function set _SpinnerDecrementButtonSkin_SolidColorStroke1(param1:SolidColorStroke) : void
      {
         var _loc2_:Object = this._1918037142_SpinnerDecrementButtonSkin_SolidColorStroke1;
         if(_loc2_ !== param1)
         {
            this._1918037142_SpinnerDecrementButtonSkin_SolidColorStroke1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_SpinnerDecrementButtonSkin_SolidColorStroke1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get arrow() : Path
      {
         return this._93090825arrow;
      }
      
      public function set arrow(param1:Path) : void
      {
         var _loc2_:Object = this._93090825arrow;
         if(_loc2_ !== param1)
         {
            this._93090825arrow = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"arrow",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get arrowFill() : SolidColor
      {
         return this._1026379476arrowFill;
      }
      
      public function set arrowFill(param1:SolidColor) : void
      {
         var _loc2_:Object = this._1026379476arrowFill;
         if(_loc2_ !== param1)
         {
            this._1026379476arrowFill = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"arrowFill",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get innerLightRect() : Rect
      {
         return this._1780999580innerLightRect;
      }
      
      public function set innerLightRect(param1:Rect) : void
      {
         var _loc2_:Object = this._1780999580innerLightRect;
         if(_loc2_ !== param1)
         {
            this._1780999580innerLightRect = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"innerLightRect",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get innerLightStroke() : Rect
      {
         return this._2100685128innerLightStroke;
      }
      
      public function set innerLightStroke(param1:Rect) : void
      {
         var _loc2_:Object = this._2100685128innerLightStroke;
         if(_loc2_ !== param1)
         {
            this._2100685128innerLightStroke = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"innerLightStroke",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get innerRect() : Rect
      {
         return this._528023206innerRect;
      }
      
      public function set innerRect(param1:Rect) : void
      {
         var _loc2_:Object = this._528023206innerRect;
         if(_loc2_ !== param1)
         {
            this._528023206innerRect = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"innerRect",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get lightRect() : Rect
      {
         return this._685606938lightRect;
      }
      
      public function set lightRect(param1:Rect) : void
      {
         var _loc2_:Object = this._685606938lightRect;
         if(_loc2_ !== param1)
         {
            this._685606938lightRect = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"lightRect",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get hostComponent() : Button
      {
         return this._213507019hostComponent;
      }
      
      public function set hostComponent(param1:Button) : void
      {
         var _loc2_:Object = this._213507019hostComponent;
         if(_loc2_ !== param1)
         {
            this._213507019hostComponent = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"hostComponent",_loc2_,param1));
            }
         }
      }
   }
}

