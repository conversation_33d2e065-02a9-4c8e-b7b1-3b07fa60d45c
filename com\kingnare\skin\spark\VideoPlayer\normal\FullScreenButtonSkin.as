package com.kingnare.skin.spark.VideoPlayer.normal
{
   import mx.binding.BindingManager;
   import mx.core.IFlexModuleFactory;
   import mx.core.IStateClient2;
   import mx.events.PropertyChangeEvent;
   import mx.graphics.GradientEntry;
   import mx.graphics.LinearGradient;
   import mx.graphics.LinearGradientStroke;
   import mx.graphics.SolidColor;
   import mx.graphics.SolidColorStroke;
   import mx.states.SetProperty;
   import mx.states.State;
   import spark.components.Button;
   import spark.components.Group;
   import spark.primitives.Rect;
   import spark.skins.SparkSkin;
   
   public class FullScreenButtonSkin extends SparkSkin implements IStateClient2
   {
      
      private static const exclusions:Array = ["fullScreenSymbol"];
      
      private static const symbols:Array = [];
      
      private var _1393743717_FullScreenButtonSkin_GradientEntry1:GradientEntry;
      
      private var _1393743718_FullScreenButtonSkin_GradientEntry2:GradientEntry;
      
      private var _2143405741fullScreenSymbol:Group;
      
      private var _224580727fullScreenSymbolBottomLeftFill1:SolidColor;
      
      private var _224580726fullScreenSymbolBottomLeftFill2:SolidColor;
      
      private var _1518768560fullScreenSymbolBottomRightFill1:SolidColor;
      
      private var _1518768559fullScreenSymbolBottomRightFill2:SolidColor;
      
      private var _865374853fullScreenSymbolFill1:SolidColor;
      
      private var _865374852fullScreenSymbolFill2:SolidColor;
      
      private var _865374851fullScreenSymbolFill3:SolidColor;
      
      private var _1738690149fullScreenSymbolTopLeftFill1:SolidColor;
      
      private var _1738690150fullScreenSymbolTopLeftFill2:SolidColor;
      
      private var _786913548fullScreenSymbolTopRightFill1:SolidColor;
      
      private var _786913547fullScreenSymbolTopRightFill2:SolidColor;
      
      private var __moduleFactoryInitialized:Boolean = false;
      
      private var _213507019hostComponent:Button;
      
      public function FullScreenButtonSkin()
      {
         super();
         mx_internal::_document = this;
         this.mxmlContent = [this._FullScreenButtonSkin_Rect1_c(),this._FullScreenButtonSkin_Rect2_c(),this._FullScreenButtonSkin_Group1_i()];
         this.currentState = "up";
         states = [new State({
            "name":"up",
            "overrides":[]
         }),new State({
            "name":"over",
            "overrides":[new SetProperty().initializeFromObject({
               "target":"_FullScreenButtonSkin_GradientEntry1",
               "name":"alpha",
               "value":1
            }),new SetProperty().initializeFromObject({
               "target":"_FullScreenButtonSkin_GradientEntry2",
               "name":"alpha",
               "value":1
            }),new SetProperty().initializeFromObject({
               "target":"fullScreenSymbol",
               "name":"alpha",
               "value":1
            })]
         }),new State({
            "name":"down",
            "overrides":[]
         }),new State({
            "name":"disabled",
            "overrides":[new SetProperty().initializeFromObject({
               "name":"alpha",
               "value":0.5
            })]
         })];
      }
      
      override public function set moduleFactory(param1:IFlexModuleFactory) : void
      {
         super.moduleFactory = param1;
         if(this.__moduleFactoryInitialized)
         {
            return;
         }
         this.__moduleFactoryInitialized = true;
      }
      
      override public function initialize() : void
      {
         super.initialize();
      }
      
      override public function get colorizeExclusions() : Array
      {
         return exclusions;
      }
      
      override public function get symbolItems() : Array
      {
         return symbols;
      }
      
      override protected function initializationComplete() : void
      {
         useChromeColor = true;
         super.initializationComplete();
      }
      
      private function _FullScreenButtonSkin_Rect1_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 0;
         _loc1_.right = 0;
         _loc1_.top = 0;
         _loc1_.bottom = 0;
         _loc1_.width = 38;
         _loc1_.height = 24;
         _loc1_.fill = this._FullScreenButtonSkin_LinearGradient1_c();
         _loc1_.stroke = this._FullScreenButtonSkin_SolidColorStroke1_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _FullScreenButtonSkin_LinearGradient1_c() : LinearGradient
      {
         var _loc1_:LinearGradient = new LinearGradient();
         _loc1_.rotation = 90;
         _loc1_.entries = [this._FullScreenButtonSkin_GradientEntry1_i(),this._FullScreenButtonSkin_GradientEntry2_i()];
         return _loc1_;
      }
      
      private function _FullScreenButtonSkin_GradientEntry1_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 5855577;
         _loc1_.alpha = 0.9;
         this._FullScreenButtonSkin_GradientEntry1 = _loc1_;
         BindingManager.executeBindings(this,"_FullScreenButtonSkin_GradientEntry1",this._FullScreenButtonSkin_GradientEntry1);
         return _loc1_;
      }
      
      private function _FullScreenButtonSkin_GradientEntry2_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 4802889;
         _loc1_.alpha = 0.9;
         this._FullScreenButtonSkin_GradientEntry2 = _loc1_;
         BindingManager.executeBindings(this,"_FullScreenButtonSkin_GradientEntry2",this._FullScreenButtonSkin_GradientEntry2);
         return _loc1_;
      }
      
      private function _FullScreenButtonSkin_SolidColorStroke1_c() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.caps = "none";
         _loc1_.color = 0;
         _loc1_.joints = "miter";
         _loc1_.miterLimit = 4;
         _loc1_.weight = 1;
         return _loc1_;
      }
      
      private function _FullScreenButtonSkin_Rect2_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 1;
         _loc1_.right = 1;
         _loc1_.top = 1;
         _loc1_.bottom = 1;
         _loc1_.stroke = this._FullScreenButtonSkin_LinearGradientStroke1_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _FullScreenButtonSkin_LinearGradientStroke1_c() : LinearGradientStroke
      {
         var _loc1_:LinearGradientStroke = new LinearGradientStroke();
         _loc1_.rotation = 90;
         _loc1_.joints = "miter";
         _loc1_.miterLimit = 4;
         _loc1_.weight = 1;
         _loc1_.entries = [this._FullScreenButtonSkin_GradientEntry3_c(),this._FullScreenButtonSkin_GradientEntry4_c()];
         return _loc1_;
      }
      
      private function _FullScreenButtonSkin_GradientEntry3_c() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 6710886;
         return _loc1_;
      }
      
      private function _FullScreenButtonSkin_GradientEntry4_c() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 5066061;
         return _loc1_;
      }
      
      private function _FullScreenButtonSkin_Group1_i() : Group
      {
         var _loc1_:Group = new Group();
         _loc1_.horizontalCenter = 0;
         _loc1_.verticalCenter = 0;
         _loc1_.alpha = 0.8;
         _loc1_.mxmlContent = [this._FullScreenButtonSkin_Rect3_c(),this._FullScreenButtonSkin_Rect4_c(),this._FullScreenButtonSkin_Rect5_c(),this._FullScreenButtonSkin_Rect6_c(),this._FullScreenButtonSkin_Rect7_c(),this._FullScreenButtonSkin_Rect8_c(),this._FullScreenButtonSkin_Rect9_c(),this._FullScreenButtonSkin_Rect10_c(),this._FullScreenButtonSkin_Rect11_c(),this._FullScreenButtonSkin_Rect12_c(),this._FullScreenButtonSkin_Rect13_c(),this._FullScreenButtonSkin_Rect14_c()];
         _loc1_.id = "fullScreenSymbol";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         this.fullScreenSymbol = _loc1_;
         BindingManager.executeBindings(this,"fullScreenSymbol",this.fullScreenSymbol);
         return _loc1_;
      }
      
      private function _FullScreenButtonSkin_Rect3_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 0;
         _loc1_.top = 0;
         _loc1_.width = 19;
         _loc1_.height = 14;
         _loc1_.fill = this._FullScreenButtonSkin_SolidColor1_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _FullScreenButtonSkin_SolidColor1_c() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.alpha = 0;
         return _loc1_;
      }
      
      private function _FullScreenButtonSkin_Rect4_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 2;
         _loc1_.top = 2;
         _loc1_.right = 2;
         _loc1_.bottom = 2;
         _loc1_.fill = this._FullScreenButtonSkin_SolidColor2_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _FullScreenButtonSkin_SolidColor2_i() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 16777215;
         this.fullScreenSymbolFill1 = _loc1_;
         BindingManager.executeBindings(this,"fullScreenSymbolFill1",this.fullScreenSymbolFill1);
         return _loc1_;
      }
      
      private function _FullScreenButtonSkin_Rect5_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 4;
         _loc1_.top = 4;
         _loc1_.right = 4;
         _loc1_.bottom = 4;
         _loc1_.fill = this._FullScreenButtonSkin_SolidColor3_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _FullScreenButtonSkin_SolidColor3_i() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 16777215;
         this.fullScreenSymbolFill2 = _loc1_;
         BindingManager.executeBindings(this,"fullScreenSymbolFill2",this.fullScreenSymbolFill2);
         return _loc1_;
      }
      
      private function _FullScreenButtonSkin_Rect6_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 5;
         _loc1_.top = 5;
         _loc1_.right = 5;
         _loc1_.bottom = 5;
         _loc1_.fill = this._FullScreenButtonSkin_SolidColor4_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _FullScreenButtonSkin_SolidColor4_i() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 16777215;
         this.fullScreenSymbolFill3 = _loc1_;
         BindingManager.executeBindings(this,"fullScreenSymbolFill3",this.fullScreenSymbolFill3);
         return _loc1_;
      }
      
      private function _FullScreenButtonSkin_Rect7_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 0;
         _loc1_.top = 0;
         _loc1_.width = 3;
         _loc1_.height = 1;
         _loc1_.fill = this._FullScreenButtonSkin_SolidColor5_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _FullScreenButtonSkin_SolidColor5_i() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 16777215;
         this.fullScreenSymbolTopLeftFill1 = _loc1_;
         BindingManager.executeBindings(this,"fullScreenSymbolTopLeftFill1",this.fullScreenSymbolTopLeftFill1);
         return _loc1_;
      }
      
      private function _FullScreenButtonSkin_Rect8_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 0;
         _loc1_.top = 1;
         _loc1_.width = 1;
         _loc1_.height = 2;
         _loc1_.fill = this._FullScreenButtonSkin_SolidColor6_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _FullScreenButtonSkin_SolidColor6_i() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 16777215;
         this.fullScreenSymbolTopLeftFill2 = _loc1_;
         BindingManager.executeBindings(this,"fullScreenSymbolTopLeftFill2",this.fullScreenSymbolTopLeftFill2);
         return _loc1_;
      }
      
      private function _FullScreenButtonSkin_Rect9_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.right = 0;
         _loc1_.top = 0;
         _loc1_.width = 3;
         _loc1_.height = 1;
         _loc1_.fill = this._FullScreenButtonSkin_SolidColor7_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _FullScreenButtonSkin_SolidColor7_i() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 16777215;
         this.fullScreenSymbolTopRightFill1 = _loc1_;
         BindingManager.executeBindings(this,"fullScreenSymbolTopRightFill1",this.fullScreenSymbolTopRightFill1);
         return _loc1_;
      }
      
      private function _FullScreenButtonSkin_Rect10_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.right = 0;
         _loc1_.top = 1;
         _loc1_.width = 1;
         _loc1_.height = 2;
         _loc1_.fill = this._FullScreenButtonSkin_SolidColor8_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _FullScreenButtonSkin_SolidColor8_i() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 16777215;
         this.fullScreenSymbolTopRightFill2 = _loc1_;
         BindingManager.executeBindings(this,"fullScreenSymbolTopRightFill2",this.fullScreenSymbolTopRightFill2);
         return _loc1_;
      }
      
      private function _FullScreenButtonSkin_Rect11_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 0;
         _loc1_.bottom = 0;
         _loc1_.width = 3;
         _loc1_.height = 1;
         _loc1_.fill = this._FullScreenButtonSkin_SolidColor9_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _FullScreenButtonSkin_SolidColor9_i() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 16777215;
         this.fullScreenSymbolBottomLeftFill1 = _loc1_;
         BindingManager.executeBindings(this,"fullScreenSymbolBottomLeftFill1",this.fullScreenSymbolBottomLeftFill1);
         return _loc1_;
      }
      
      private function _FullScreenButtonSkin_Rect12_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 0;
         _loc1_.bottom = 1;
         _loc1_.width = 1;
         _loc1_.height = 2;
         _loc1_.fill = this._FullScreenButtonSkin_SolidColor10_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _FullScreenButtonSkin_SolidColor10_i() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 16777215;
         this.fullScreenSymbolBottomLeftFill2 = _loc1_;
         BindingManager.executeBindings(this,"fullScreenSymbolBottomLeftFill2",this.fullScreenSymbolBottomLeftFill2);
         return _loc1_;
      }
      
      private function _FullScreenButtonSkin_Rect13_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.right = 0;
         _loc1_.bottom = 0;
         _loc1_.width = 3;
         _loc1_.height = 1;
         _loc1_.fill = this._FullScreenButtonSkin_SolidColor11_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _FullScreenButtonSkin_SolidColor11_i() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 16777215;
         this.fullScreenSymbolBottomRightFill1 = _loc1_;
         BindingManager.executeBindings(this,"fullScreenSymbolBottomRightFill1",this.fullScreenSymbolBottomRightFill1);
         return _loc1_;
      }
      
      private function _FullScreenButtonSkin_Rect14_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.right = 0;
         _loc1_.bottom = 1;
         _loc1_.width = 1;
         _loc1_.height = 2;
         _loc1_.fill = this._FullScreenButtonSkin_SolidColor12_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _FullScreenButtonSkin_SolidColor12_i() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 16777215;
         this.fullScreenSymbolBottomRightFill2 = _loc1_;
         BindingManager.executeBindings(this,"fullScreenSymbolBottomRightFill2",this.fullScreenSymbolBottomRightFill2);
         return _loc1_;
      }
      
      [Bindable(event="propertyChange")]
      public function get _FullScreenButtonSkin_GradientEntry1() : GradientEntry
      {
         return this._1393743717_FullScreenButtonSkin_GradientEntry1;
      }
      
      public function set _FullScreenButtonSkin_GradientEntry1(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._1393743717_FullScreenButtonSkin_GradientEntry1;
         if(_loc2_ !== param1)
         {
            this._1393743717_FullScreenButtonSkin_GradientEntry1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_FullScreenButtonSkin_GradientEntry1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _FullScreenButtonSkin_GradientEntry2() : GradientEntry
      {
         return this._1393743718_FullScreenButtonSkin_GradientEntry2;
      }
      
      public function set _FullScreenButtonSkin_GradientEntry2(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._1393743718_FullScreenButtonSkin_GradientEntry2;
         if(_loc2_ !== param1)
         {
            this._1393743718_FullScreenButtonSkin_GradientEntry2 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_FullScreenButtonSkin_GradientEntry2",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get fullScreenSymbol() : Group
      {
         return this._2143405741fullScreenSymbol;
      }
      
      public function set fullScreenSymbol(param1:Group) : void
      {
         var _loc2_:Object = this._2143405741fullScreenSymbol;
         if(_loc2_ !== param1)
         {
            this._2143405741fullScreenSymbol = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"fullScreenSymbol",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get fullScreenSymbolBottomLeftFill1() : SolidColor
      {
         return this._224580727fullScreenSymbolBottomLeftFill1;
      }
      
      public function set fullScreenSymbolBottomLeftFill1(param1:SolidColor) : void
      {
         var _loc2_:Object = this._224580727fullScreenSymbolBottomLeftFill1;
         if(_loc2_ !== param1)
         {
            this._224580727fullScreenSymbolBottomLeftFill1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"fullScreenSymbolBottomLeftFill1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get fullScreenSymbolBottomLeftFill2() : SolidColor
      {
         return this._224580726fullScreenSymbolBottomLeftFill2;
      }
      
      public function set fullScreenSymbolBottomLeftFill2(param1:SolidColor) : void
      {
         var _loc2_:Object = this._224580726fullScreenSymbolBottomLeftFill2;
         if(_loc2_ !== param1)
         {
            this._224580726fullScreenSymbolBottomLeftFill2 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"fullScreenSymbolBottomLeftFill2",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get fullScreenSymbolBottomRightFill1() : SolidColor
      {
         return this._1518768560fullScreenSymbolBottomRightFill1;
      }
      
      public function set fullScreenSymbolBottomRightFill1(param1:SolidColor) : void
      {
         var _loc2_:Object = this._1518768560fullScreenSymbolBottomRightFill1;
         if(_loc2_ !== param1)
         {
            this._1518768560fullScreenSymbolBottomRightFill1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"fullScreenSymbolBottomRightFill1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get fullScreenSymbolBottomRightFill2() : SolidColor
      {
         return this._1518768559fullScreenSymbolBottomRightFill2;
      }
      
      public function set fullScreenSymbolBottomRightFill2(param1:SolidColor) : void
      {
         var _loc2_:Object = this._1518768559fullScreenSymbolBottomRightFill2;
         if(_loc2_ !== param1)
         {
            this._1518768559fullScreenSymbolBottomRightFill2 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"fullScreenSymbolBottomRightFill2",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get fullScreenSymbolFill1() : SolidColor
      {
         return this._865374853fullScreenSymbolFill1;
      }
      
      public function set fullScreenSymbolFill1(param1:SolidColor) : void
      {
         var _loc2_:Object = this._865374853fullScreenSymbolFill1;
         if(_loc2_ !== param1)
         {
            this._865374853fullScreenSymbolFill1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"fullScreenSymbolFill1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get fullScreenSymbolFill2() : SolidColor
      {
         return this._865374852fullScreenSymbolFill2;
      }
      
      public function set fullScreenSymbolFill2(param1:SolidColor) : void
      {
         var _loc2_:Object = this._865374852fullScreenSymbolFill2;
         if(_loc2_ !== param1)
         {
            this._865374852fullScreenSymbolFill2 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"fullScreenSymbolFill2",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get fullScreenSymbolFill3() : SolidColor
      {
         return this._865374851fullScreenSymbolFill3;
      }
      
      public function set fullScreenSymbolFill3(param1:SolidColor) : void
      {
         var _loc2_:Object = this._865374851fullScreenSymbolFill3;
         if(_loc2_ !== param1)
         {
            this._865374851fullScreenSymbolFill3 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"fullScreenSymbolFill3",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get fullScreenSymbolTopLeftFill1() : SolidColor
      {
         return this._1738690149fullScreenSymbolTopLeftFill1;
      }
      
      public function set fullScreenSymbolTopLeftFill1(param1:SolidColor) : void
      {
         var _loc2_:Object = this._1738690149fullScreenSymbolTopLeftFill1;
         if(_loc2_ !== param1)
         {
            this._1738690149fullScreenSymbolTopLeftFill1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"fullScreenSymbolTopLeftFill1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get fullScreenSymbolTopLeftFill2() : SolidColor
      {
         return this._1738690150fullScreenSymbolTopLeftFill2;
      }
      
      public function set fullScreenSymbolTopLeftFill2(param1:SolidColor) : void
      {
         var _loc2_:Object = this._1738690150fullScreenSymbolTopLeftFill2;
         if(_loc2_ !== param1)
         {
            this._1738690150fullScreenSymbolTopLeftFill2 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"fullScreenSymbolTopLeftFill2",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get fullScreenSymbolTopRightFill1() : SolidColor
      {
         return this._786913548fullScreenSymbolTopRightFill1;
      }
      
      public function set fullScreenSymbolTopRightFill1(param1:SolidColor) : void
      {
         var _loc2_:Object = this._786913548fullScreenSymbolTopRightFill1;
         if(_loc2_ !== param1)
         {
            this._786913548fullScreenSymbolTopRightFill1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"fullScreenSymbolTopRightFill1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get fullScreenSymbolTopRightFill2() : SolidColor
      {
         return this._786913547fullScreenSymbolTopRightFill2;
      }
      
      public function set fullScreenSymbolTopRightFill2(param1:SolidColor) : void
      {
         var _loc2_:Object = this._786913547fullScreenSymbolTopRightFill2;
         if(_loc2_ !== param1)
         {
            this._786913547fullScreenSymbolTopRightFill2 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"fullScreenSymbolTopRightFill2",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get hostComponent() : Button
      {
         return this._213507019hostComponent;
      }
      
      public function set hostComponent(param1:Button) : void
      {
         var _loc2_:Object = this._213507019hostComponent;
         if(_loc2_ !== param1)
         {
            this._213507019hostComponent = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"hostComponent",_loc2_,param1));
            }
         }
      }
   }
}

