package com.kingnare.skin.spark.VideoPlayer.fullScreen
{
   import mx.binding.BindingManager;
   import mx.core.DeferredInstanceFromFunction;
   import mx.core.IFlexModuleFactory;
   import mx.core.IStateClient2;
   import mx.events.PropertyChangeEvent;
   import mx.graphics.GradientEntry;
   import mx.graphics.LinearGradient;
   import mx.graphics.LinearGradientStroke;
   import mx.graphics.SolidColor;
   import mx.graphics.SolidColorStroke;
   import mx.states.AddItems;
   import mx.states.SetProperty;
   import mx.states.State;
   import spark.components.Group;
   import spark.components.ToggleButton;
   import spark.primitives.Path;
   import spark.primitives.Rect;
   import spark.skins.SparkSkin;
   
   public class PlayPauseButtonSkin extends SparkSkin implements IStateClient2
   {
      
      private static const exclusions:Array = ["playSymbol","pauseSymbol"];
      
      private static const symbols:Array = [];
      
      private var _110636132_PlayPauseButtonSkin_GradientEntry1:GradientEntry;
      
      private var _110636131_PlayPauseButtonSkin_GradientEntry2:GradientEntry;
      
      public var _PlayPauseButtonSkin_Rect2:Rect;
      
      private var _88293235_PlayPauseButtonSkin_SolidColor2:SolidColor;
      
      private var _88293234_PlayPauseButtonSkin_SolidColor3:SolidColor;
      
      private var _1801210066pauseSymbol:Group;
      
      private var _1587721708playSymbol:Group;
      
      private var _1096763791playSymbolFill:SolidColor;
      
      private var __moduleFactoryInitialized:Boolean = false;
      
      private var _213507019hostComponent:ToggleButton;
      
      public function PlayPauseButtonSkin()
      {
         super();
         mx_internal::_document = this;
         this.mxmlContent = [this._PlayPauseButtonSkin_Rect1_c(),this._PlayPauseButtonSkin_Rect2_i()];
         this.currentState = "up";
         var _loc1_:DeferredInstanceFromFunction = new DeferredInstanceFromFunction(this._PlayPauseButtonSkin_Group1_i);
         var _loc2_:DeferredInstanceFromFunction = new DeferredInstanceFromFunction(this._PlayPauseButtonSkin_Group2_i);
         states = [new State({
            "name":"up",
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_loc1_,
               "destination":null,
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["_PlayPauseButtonSkin_Rect2"]
            })]
         }),new State({
            "name":"over",
            "stateGroups":["overStates"],
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_loc1_,
               "destination":null,
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["_PlayPauseButtonSkin_Rect2"]
            }),new SetProperty().initializeFromObject({
               "target":"_PlayPauseButtonSkin_GradientEntry1",
               "name":"alpha",
               "value":1
            }),new SetProperty().initializeFromObject({
               "target":"_PlayPauseButtonSkin_GradientEntry2",
               "name":"alpha",
               "value":1
            }),new SetProperty().initializeFromObject({
               "target":"playSymbolFill",
               "name":"alpha",
               "value":1
            }),new SetProperty().initializeFromObject({
               "target":"_PlayPauseButtonSkin_SolidColor2",
               "name":"alpha",
               "value":1
            }),new SetProperty().initializeFromObject({
               "target":"_PlayPauseButtonSkin_SolidColor3",
               "name":"alpha",
               "value":1
            })]
         }),new State({
            "name":"down",
            "stateGroups":["downStates"],
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_loc1_,
               "destination":null,
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["_PlayPauseButtonSkin_Rect2"]
            })]
         }),new State({
            "name":"disabled",
            "stateGroups":["disabledStates"],
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_loc1_,
               "destination":null,
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["_PlayPauseButtonSkin_Rect2"]
            }),new SetProperty().initializeFromObject({
               "name":"alpha",
               "value":0.5
            })]
         }),new State({
            "name":"upAndSelected",
            "stateGroups":["selectedUpStates","selectedStates"],
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_loc2_,
               "destination":null,
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["_PlayPauseButtonSkin_Rect2"]
            })]
         }),new State({
            "name":"overAndSelected",
            "stateGroups":["overStates","selectedStates"],
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_loc2_,
               "destination":null,
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["_PlayPauseButtonSkin_Rect2"]
            }),new SetProperty().initializeFromObject({
               "target":"_PlayPauseButtonSkin_GradientEntry1",
               "name":"alpha",
               "value":1
            }),new SetProperty().initializeFromObject({
               "target":"_PlayPauseButtonSkin_GradientEntry2",
               "name":"alpha",
               "value":1
            }),new SetProperty().initializeFromObject({
               "target":"playSymbolFill",
               "name":"alpha",
               "value":1
            }),new SetProperty().initializeFromObject({
               "target":"_PlayPauseButtonSkin_SolidColor2",
               "name":"alpha",
               "value":1
            }),new SetProperty().initializeFromObject({
               "target":"_PlayPauseButtonSkin_SolidColor3",
               "name":"alpha",
               "value":1
            })]
         }),new State({
            "name":"downAndSelected",
            "stateGroups":["downStates","selectedStates"],
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_loc2_,
               "destination":null,
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["_PlayPauseButtonSkin_Rect2"]
            })]
         }),new State({
            "name":"disabledAndSelected",
            "stateGroups":["disabledStates","selectedUpStates","selectedStates"],
            "overrides":[new AddItems().initializeFromObject({
               "itemsFactory":_loc2_,
               "destination":null,
               "propertyName":"mxmlContent",
               "position":"after",
               "relativeTo":["_PlayPauseButtonSkin_Rect2"]
            }),new SetProperty().initializeFromObject({
               "name":"alpha",
               "value":0.5
            })]
         })];
      }
      
      override public function set moduleFactory(param1:IFlexModuleFactory) : void
      {
         super.moduleFactory = param1;
         if(this.__moduleFactoryInitialized)
         {
            return;
         }
         this.__moduleFactoryInitialized = true;
      }
      
      override public function initialize() : void
      {
         super.initialize();
      }
      
      override public function get colorizeExclusions() : Array
      {
         return exclusions;
      }
      
      override public function get symbolItems() : Array
      {
         return symbols;
      }
      
      override protected function initializationComplete() : void
      {
         useChromeColor = true;
         super.initializationComplete();
      }
      
      private function _PlayPauseButtonSkin_Rect1_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 0;
         _loc1_.right = 0;
         _loc1_.top = 0;
         _loc1_.bottom = 0;
         _loc1_.width = 38;
         _loc1_.height = 24;
         _loc1_.fill = this._PlayPauseButtonSkin_LinearGradient1_c();
         _loc1_.stroke = this._PlayPauseButtonSkin_SolidColorStroke1_c();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _PlayPauseButtonSkin_LinearGradient1_c() : LinearGradient
      {
         var _loc1_:LinearGradient = new LinearGradient();
         _loc1_.rotation = 90;
         _loc1_.entries = [this._PlayPauseButtonSkin_GradientEntry1_i(),this._PlayPauseButtonSkin_GradientEntry2_i()];
         return _loc1_;
      }
      
      private function _PlayPauseButtonSkin_GradientEntry1_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 5855577;
         _loc1_.alpha = 0.9;
         this._PlayPauseButtonSkin_GradientEntry1 = _loc1_;
         BindingManager.executeBindings(this,"_PlayPauseButtonSkin_GradientEntry1",this._PlayPauseButtonSkin_GradientEntry1);
         return _loc1_;
      }
      
      private function _PlayPauseButtonSkin_GradientEntry2_i() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 4802889;
         _loc1_.alpha = 0.9;
         this._PlayPauseButtonSkin_GradientEntry2 = _loc1_;
         BindingManager.executeBindings(this,"_PlayPauseButtonSkin_GradientEntry2",this._PlayPauseButtonSkin_GradientEntry2);
         return _loc1_;
      }
      
      private function _PlayPauseButtonSkin_SolidColorStroke1_c() : SolidColorStroke
      {
         var _loc1_:SolidColorStroke = new SolidColorStroke();
         _loc1_.caps = "none";
         _loc1_.color = 0;
         _loc1_.joints = "miter";
         _loc1_.miterLimit = 4;
         _loc1_.weight = 1;
         return _loc1_;
      }
      
      private function _PlayPauseButtonSkin_Rect2_i() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 1;
         _loc1_.right = 1;
         _loc1_.top = 1;
         _loc1_.bottom = 1;
         _loc1_.stroke = this._PlayPauseButtonSkin_LinearGradientStroke1_c();
         _loc1_.initialized(this,"_PlayPauseButtonSkin_Rect2");
         this._PlayPauseButtonSkin_Rect2 = _loc1_;
         BindingManager.executeBindings(this,"_PlayPauseButtonSkin_Rect2",this._PlayPauseButtonSkin_Rect2);
         return _loc1_;
      }
      
      private function _PlayPauseButtonSkin_LinearGradientStroke1_c() : LinearGradientStroke
      {
         var _loc1_:LinearGradientStroke = new LinearGradientStroke();
         _loc1_.rotation = 90;
         _loc1_.joints = "miter";
         _loc1_.miterLimit = 4;
         _loc1_.weight = 1;
         _loc1_.entries = [this._PlayPauseButtonSkin_GradientEntry3_c(),this._PlayPauseButtonSkin_GradientEntry4_c()];
         return _loc1_;
      }
      
      private function _PlayPauseButtonSkin_GradientEntry3_c() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 6710886;
         return _loc1_;
      }
      
      private function _PlayPauseButtonSkin_GradientEntry4_c() : GradientEntry
      {
         var _loc1_:GradientEntry = new GradientEntry();
         _loc1_.color = 5066061;
         return _loc1_;
      }
      
      private function _PlayPauseButtonSkin_Group1_i() : Group
      {
         var _loc1_:Group = new Group();
         _loc1_.horizontalCenter = 0;
         _loc1_.verticalCenter = -1;
         _loc1_.mxmlContent = [this._PlayPauseButtonSkin_Path1_c()];
         _loc1_.id = "playSymbol";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         this.playSymbol = _loc1_;
         BindingManager.executeBindings(this,"playSymbol",this.playSymbol);
         return _loc1_;
      }
      
      private function _PlayPauseButtonSkin_Path1_c() : Path
      {
         var _loc1_:Path = new Path();
         _loc1_.data = "M 1 2 L 2 2 L 3 2 L 3 3 L 4 3 L 4 4 L 5 4 L 5 5 L 6 5 L 6 6 L 7 6 L 7 7 L 7 9 L 6 9 L 6 10 L 5 10 L 5 11 L 4 11 L 4 12 L 3 12 L 3 13 L 1 13 Z";
         _loc1_.winding = "evenOdd";
         _loc1_.fill = this._PlayPauseButtonSkin_SolidColor1_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _PlayPauseButtonSkin_SolidColor1_i() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 16777215;
         _loc1_.alpha = 0.8;
         this.playSymbolFill = _loc1_;
         BindingManager.executeBindings(this,"playSymbolFill",this.playSymbolFill);
         return _loc1_;
      }
      
      private function _PlayPauseButtonSkin_Group2_i() : Group
      {
         var _loc1_:Group = new Group();
         _loc1_.horizontalCenter = 0;
         _loc1_.verticalCenter = 0;
         _loc1_.mxmlContent = [this._PlayPauseButtonSkin_Rect3_c(),this._PlayPauseButtonSkin_Rect4_c()];
         _loc1_.id = "pauseSymbol";
         if(!_loc1_.document)
         {
            _loc1_.document = this;
         }
         this.pauseSymbol = _loc1_;
         BindingManager.executeBindings(this,"pauseSymbol",this.pauseSymbol);
         return _loc1_;
      }
      
      private function _PlayPauseButtonSkin_Rect3_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 0;
         _loc1_.top = 0;
         _loc1_.height = 11;
         _loc1_.width = 3;
         _loc1_.fill = this._PlayPauseButtonSkin_SolidColor2_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _PlayPauseButtonSkin_SolidColor2_i() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 16777215;
         _loc1_.alpha = 0.8;
         this._PlayPauseButtonSkin_SolidColor2 = _loc1_;
         BindingManager.executeBindings(this,"_PlayPauseButtonSkin_SolidColor2",this._PlayPauseButtonSkin_SolidColor2);
         return _loc1_;
      }
      
      private function _PlayPauseButtonSkin_Rect4_c() : Rect
      {
         var _loc1_:Rect = new Rect();
         _loc1_.left = 4;
         _loc1_.top = 0;
         _loc1_.height = 11;
         _loc1_.width = 3;
         _loc1_.fill = this._PlayPauseButtonSkin_SolidColor3_i();
         _loc1_.initialized(this,null);
         return _loc1_;
      }
      
      private function _PlayPauseButtonSkin_SolidColor3_i() : SolidColor
      {
         var _loc1_:SolidColor = new SolidColor();
         _loc1_.color = 16777215;
         _loc1_.alpha = 0.85;
         this._PlayPauseButtonSkin_SolidColor3 = _loc1_;
         BindingManager.executeBindings(this,"_PlayPauseButtonSkin_SolidColor3",this._PlayPauseButtonSkin_SolidColor3);
         return _loc1_;
      }
      
      [Bindable(event="propertyChange")]
      public function get _PlayPauseButtonSkin_GradientEntry1() : GradientEntry
      {
         return this._110636132_PlayPauseButtonSkin_GradientEntry1;
      }
      
      public function set _PlayPauseButtonSkin_GradientEntry1(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._110636132_PlayPauseButtonSkin_GradientEntry1;
         if(_loc2_ !== param1)
         {
            this._110636132_PlayPauseButtonSkin_GradientEntry1 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_PlayPauseButtonSkin_GradientEntry1",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _PlayPauseButtonSkin_GradientEntry2() : GradientEntry
      {
         return this._110636131_PlayPauseButtonSkin_GradientEntry2;
      }
      
      public function set _PlayPauseButtonSkin_GradientEntry2(param1:GradientEntry) : void
      {
         var _loc2_:Object = this._110636131_PlayPauseButtonSkin_GradientEntry2;
         if(_loc2_ !== param1)
         {
            this._110636131_PlayPauseButtonSkin_GradientEntry2 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_PlayPauseButtonSkin_GradientEntry2",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _PlayPauseButtonSkin_SolidColor2() : SolidColor
      {
         return this._88293235_PlayPauseButtonSkin_SolidColor2;
      }
      
      public function set _PlayPauseButtonSkin_SolidColor2(param1:SolidColor) : void
      {
         var _loc2_:Object = this._88293235_PlayPauseButtonSkin_SolidColor2;
         if(_loc2_ !== param1)
         {
            this._88293235_PlayPauseButtonSkin_SolidColor2 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_PlayPauseButtonSkin_SolidColor2",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get _PlayPauseButtonSkin_SolidColor3() : SolidColor
      {
         return this._88293234_PlayPauseButtonSkin_SolidColor3;
      }
      
      public function set _PlayPauseButtonSkin_SolidColor3(param1:SolidColor) : void
      {
         var _loc2_:Object = this._88293234_PlayPauseButtonSkin_SolidColor3;
         if(_loc2_ !== param1)
         {
            this._88293234_PlayPauseButtonSkin_SolidColor3 = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"_PlayPauseButtonSkin_SolidColor3",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get pauseSymbol() : Group
      {
         return this._1801210066pauseSymbol;
      }
      
      public function set pauseSymbol(param1:Group) : void
      {
         var _loc2_:Object = this._1801210066pauseSymbol;
         if(_loc2_ !== param1)
         {
            this._1801210066pauseSymbol = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"pauseSymbol",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get playSymbol() : Group
      {
         return this._1587721708playSymbol;
      }
      
      public function set playSymbol(param1:Group) : void
      {
         var _loc2_:Object = this._1587721708playSymbol;
         if(_loc2_ !== param1)
         {
            this._1587721708playSymbol = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"playSymbol",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get playSymbolFill() : SolidColor
      {
         return this._1096763791playSymbolFill;
      }
      
      public function set playSymbolFill(param1:SolidColor) : void
      {
         var _loc2_:Object = this._1096763791playSymbolFill;
         if(_loc2_ !== param1)
         {
            this._1096763791playSymbolFill = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"playSymbolFill",_loc2_,param1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function get hostComponent() : ToggleButton
      {
         return this._213507019hostComponent;
      }
      
      public function set hostComponent(param1:ToggleButton) : void
      {
         var _loc2_:Object = this._213507019hostComponent;
         if(_loc2_ !== param1)
         {
            this._213507019hostComponent = param1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"hostComponent",_loc2_,param1));
            }
         }
      }
   }
}

