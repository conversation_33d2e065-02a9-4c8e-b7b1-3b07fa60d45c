package flashx.textLayout.formats
{
   public final class LeadingModel
   {
      
      public static const ROMAN_UP:String = "romanUp";
      
      public static const IDEOGRAPHIC_TOP_UP:String = "ideographicTopUp";
      
      public static const IDEOGRAPHIC_CENTER_UP:String = "ideographicCenterUp";
      
      public static const IDEOGRAPHIC_TOP_DOWN:String = "ideographicTopDown";
      
      public static const IDEOGRAPHIC_CENTER_DOWN:String = "ideographicCenterDown";
      
      public static const ASCENT_DESCENT_UP:String = "ascentDescentUp";
      
      public static const AUTO:String = "auto";
      
      public static const APPROXIMATE_TEXT_FIELD:String = "approximateTextField";
      
      public static const BOX:String = "box";
      
      public function LeadingModel()
      {
         super();
      }
   }
}

